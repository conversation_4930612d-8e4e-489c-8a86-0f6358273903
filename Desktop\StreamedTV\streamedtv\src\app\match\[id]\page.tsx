'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useAllMatches, useAllStreamsForMatch } from '@/hooks/useApi';
import { APIMatch } from '@/types/api';
import { streamedApiUtils } from '@/services/api';
import StreamPlayer from '@/components/StreamPlayer';
import { TeamBadge, MatchPoster } from '@/components/OptimizedImage';

// Helper function to get team names
const getTeamNames = (match: APIMatch) => {
  const homeTeam = match.teams?.home?.name || 'Home Team';
  const awayTeam = match.teams?.away?.name || 'Away Team';
  return { homeTeam, awayTeam };
};

// Helper function to check if match is live
const isMatchLive = (match: APIMatch) => {
  return streamedApiUtils.isMatchLive(match.date);
};

export default function MatchPage() {
  const params = useParams();
  const matchId = params.id as string;
  
  // Get all matches to find the specific match
  const { data: allMatches, loading: matchesLoading, error: matchesError } = useAllMatches();
  
  // Find the specific match
  const match = allMatches?.find(m => m.id === matchId);
  
  // Get streams for this match
  const { 
    flatStreams, 
    loading: streamsLoading, 
    error: streamsError,
    hasStreams 
  } = useAllStreamsForMatch(match || null);

  if (matchesLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading match details...</p>
        </div>
      </div>
    );
  }

  if (matchesError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-900 border border-red-700 rounded-lg p-6">
          <h3 className="text-red-400 font-semibold mb-2">Error Loading Match</h3>
          <p className="text-red-300">{matchesError}</p>
          <Link
            href="/"
            className="inline-block mt-4 text-blue-400 hover:text-blue-300 transition-colors"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    );
  }

  if (!match) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="h-16 w-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
          </div>
          <h3 className="text-xl text-white mb-2">Match Not Found</h3>
          <p className="text-gray-400 mb-4">
            The match with ID "{matchId}" could not be found.
          </p>
          <Link
            href="/"
            className="inline-block text-blue-400 hover:text-blue-300 transition-colors"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    );
  }

  const { homeTeam, awayTeam } = getTeamNames(match);
  const isLive = isMatchLive(match);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <ol className="flex items-center space-x-2 text-sm text-gray-400">
          <li>
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
          </li>
          <li>/</li>
          <li>
            <Link href={`/sport/${match.category}`} className="hover:text-white transition-colors">
              {match.category}
            </Link>
          </li>
          <li>/</li>
          <li className="text-white">Match</li>
        </ol>
      </nav>

      {/* Match Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <span className={`text-xs px-3 py-1 rounded-full text-white ${
            isLive ? 'bg-red-600' : 'bg-blue-600'
          }`}>
            {isLive ? 'LIVE' : 'SCHEDULED'}
          </span>
          {match.popular && (
            <span className="text-yellow-400 text-sm flex items-center">
              ⭐ Popular
            </span>
          )}
          <span className="text-gray-400 text-sm">{match.category}</span>
        </div>

        <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
          {match.title}
        </h1>

        {/* Team Information */}
        <div className="flex items-center justify-center space-x-8 mb-6">
          <div className="text-center">
            <TeamBadge
              badgeId={match.teams?.home?.badge}
              teamName={homeTeam}
              size={64}
              className="mx-auto mb-2"
            />
            <h3 className="text-lg font-semibold text-white">{homeTeam}</h3>
            <p className="text-gray-400 text-sm">Home</p>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-gray-400 mb-2">VS</div>
            <div className="text-gray-500 text-sm">
              {streamedApiUtils.formatMatchDate(match.date)}
            </div>
            <div className="text-gray-500 text-sm">
              {streamedApiUtils.formatMatchTime(match.date)}
            </div>
          </div>

          <div className="text-center">
            <TeamBadge
              badgeId={match.teams?.away?.badge}
              teamName={awayTeam}
              size={64}
              className="mx-auto mb-2"
            />
            <h3 className="text-lg font-semibold text-white">{awayTeam}</h3>
            <p className="text-gray-400 text-sm">Away</p>
          </div>
        </div>

        {/* Match Poster */}
        <div className="mb-6 text-center">
          <MatchPoster
            homeBadge={match.teams?.home?.badge}
            awayBadge={match.teams?.away?.badge}
            poster={match.poster}
            matchTitle={match.title}
            homeTeam={homeTeam}
            awayTeam={awayTeam}
            size="hero"
            className="mx-auto"
            showFallback={true}
          />
        </div>
      </div>

      {/* Stream Player */}
      <div className="mb-8">
        {streamsLoading ? (
          <div className="bg-gray-900 border border-gray-800 rounded-lg p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading streams...</p>
          </div>
        ) : streamsError ? (
          <div className="bg-red-900 border border-red-700 rounded-lg p-6">
            <h3 className="text-red-400 font-semibold mb-2">Error Loading Streams</h3>
            <p className="text-red-300">{streamsError}</p>
          </div>
        ) : hasStreams ? (
          <StreamPlayer
            title={match.title}
            streams={flatStreams}
            onStreamSelect={(stream) => {
              console.log('Selected stream:', stream);
            }}
          />
        ) : (
          <div className="bg-gray-900 border border-gray-800 rounded-lg p-8 text-center">
            <h3 className="text-xl text-white mb-4">No Streams Available</h3>
            <p className="text-gray-400 mb-4">
              Streams for this match are not currently available. This could be because:
            </p>
            <ul className="text-gray-400 text-sm space-y-1 mb-6">
              <li>• The match hasn't started yet</li>
              <li>• Streams are being prepared</li>
              <li>• Technical issues with stream sources</li>
            </ul>
            <p className="text-gray-500 text-sm">
              Please check back closer to the match time or try refreshing the page.
            </p>
          </div>
        )}
      </div>

      {/* Match Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-4">Match Details</h3>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Sport:</span>
              <span className="text-white">{match.category}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Date:</span>
              <span className="text-white">{streamedApiUtils.formatMatchDate(match.date)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Time:</span>
              <span className="text-white">{streamedApiUtils.formatMatchTime(match.date)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Status:</span>
              <span className={`${isLive ? 'text-red-400' : 'text-blue-400'}`}>
                {isLive ? 'Live' : 'Scheduled'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Available Sources:</span>
              <span className="text-white">{match.sources.length}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-4">Stream Sources</h3>
          <div className="space-y-2">
            {match.sources.map((source, index) => (
              <div key={index} className="flex justify-between items-center py-2 px-3 bg-gray-800 rounded">
                <span className="text-white font-medium">{source.source.toUpperCase()}</span>
                <span className="text-gray-400 text-sm">ID: {source.id}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
