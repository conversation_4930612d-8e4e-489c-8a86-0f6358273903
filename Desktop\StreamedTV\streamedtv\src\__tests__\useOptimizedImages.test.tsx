/**
 * Image Optimization Hooks Tests
 * Tests for image preloading and optimization hooks
 */

import React from 'react';
import { renderHook, act, waitFor } from '@testing-library/react';
import { 
  useOptimizedImage, 
  useMatchImagePreloader, 
  useIntelligentPreloader,
  useImagePerformanceMonitor 
} from '@/hooks/useOptimizedImages';
import { APIMatch } from '@/types/api';

// Mock the image optimization service
const mockPreloadImage = jest.fn();
const mockGetCachedImage = jest.fn();
const mockPreloadImages = jest.fn();
const mockGetCacheStats = jest.fn();

jest.mock('@/services/imageOptimization', () => ({
  imageOptimizationService: {
    preloadImage: mockPreloadImage,
    getCachedImage: mockGetCachedImage,
    preloadImages: mockPreloadImages,
    getCacheStats: mockGetCacheStats
  },
  queueImagePreload: jest.fn(),
  getCachedImage: mockGetCachedImage,
  getImageCacheStats: mockGetCacheStats
}));

// Mock API utils
jest.mock('@/services/api', () => ({
  streamedApiUtils: {
    getImageUrl: jest.fn((id) => `https://api.streamed.su/api/images/badge/${id}.webp`),
    getMatchPosterUrl: jest.fn((home, away) => `https://api.streamed.su/api/images/poster/${home}/${away}.webp`),
    getProxiedImageUrl: jest.fn((poster) => `https://api.streamed.su/api/images/proxy/${poster}.webp`)
  }
}));

describe('useOptimizedImage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetCacheStats.mockReturnValue({
      totalCached: 0,
      loadedCount: 0,
      errorCount: 0,
      expiredCount: 0,
      queueLength: 0,
      isPreloading: false
    });
  });

  it('should return loading state initially', () => {
    const { result } = renderHook(() => useOptimizedImage('https://example.com/test.jpg'));
    
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isLoaded).toBe(false);
    expect(result.current.hasError).toBe(false);
  });

  it('should return loaded state when image is cached', () => {
    mockGetCachedImage.mockReturnValue({ loaded: true, error: false });
    
    const { result } = renderHook(() => useOptimizedImage('https://example.com/cached.jpg'));
    
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isLoaded).toBe(true);
    expect(result.current.hasError).toBe(false);
  });

  it('should return error state when image is cached with error', () => {
    mockGetCachedImage.mockReturnValue({ loaded: false, error: true });
    
    const { result } = renderHook(() => useOptimizedImage('https://example.com/error.jpg'));
    
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isLoaded).toBe(false);
    expect(result.current.hasError).toBe(true);
  });

  it('should handle undefined src', () => {
    const { result } = renderHook(() => useOptimizedImage(undefined));
    
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isLoaded).toBe(false);
    expect(result.current.hasError).toBe(false);
  });

  it('should preload image when not cached', async () => {
    mockGetCachedImage.mockReturnValue(null);
    mockPreloadImage.mockResolvedValue(new Image());
    
    const { result } = renderHook(() => useOptimizedImage('https://example.com/new.jpg'));
    
    await waitFor(() => {
      expect(mockPreloadImage).toHaveBeenCalledWith('https://example.com/new.jpg');
    });
  });

  it('should handle preload failure', async () => {
    mockGetCachedImage.mockReturnValue(null);
    mockPreloadImage.mockRejectedValue(new Error('Failed to load'));
    
    const { result } = renderHook(() => useOptimizedImage('https://example.com/fail.jpg'));
    
    await waitFor(() => {
      expect(result.current.hasError).toBe(true);
      expect(result.current.isLoading).toBe(false);
    });
  });
});

describe('useMatchImagePreloader', () => {
  const mockMatches: APIMatch[] = [
    {
      id: '1',
      title: 'Lakers vs Warriors',
      category: 'basketball',
      date: Date.now(),
      popular: true,
      sources: [],
      teams: {
        home: { name: 'Lakers', badge: 'lakers' },
        away: { name: 'Warriors', badge: 'warriors' }
      },
      poster: 'lakers-warriors'
    },
    {
      id: '2',
      title: 'Real Madrid vs Barcelona',
      category: 'football',
      date: Date.now(),
      popular: false,
      sources: [],
      teams: {
        home: { name: 'Real Madrid', badge: 'real-madrid' },
        away: { name: 'Barcelona', badge: 'barcelona' }
      }
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockPreloadImage.mockResolvedValue(new Image());
  });

  it('should calculate total images correctly', () => {
    const { result } = renderHook(() => useMatchImagePreloader(mockMatches));
    
    // Should count: 2 home badges + 2 away badges + 1 poster + 1 generated poster = 6 images
    expect(result.current.totalImages).toBe(6);
  });

  it('should start with zero preloaded count', () => {
    const { result } = renderHook(() => useMatchImagePreloader(mockMatches));
    
    expect(result.current.preloadedCount).toBe(0);
    expect(result.current.progress).toBe(0);
    expect(result.current.isComplete).toBe(false);
  });

  it('should handle empty matches array', () => {
    const { result } = renderHook(() => useMatchImagePreloader([]));
    
    expect(result.current.totalImages).toBe(0);
    expect(result.current.preloadedCount).toBe(0);
    expect(result.current.progress).toBe(0);
    expect(result.current.isComplete).toBe(false);
  });

  it('should update progress as images load', async () => {
    const { result } = renderHook(() => useMatchImagePreloader(mockMatches));
    
    // Wait for preloading to start
    await waitFor(() => {
      expect(mockPreloadImage).toHaveBeenCalled();
    });
    
    // Progress should be calculated correctly
    expect(result.current.progress).toBeGreaterThanOrEqual(0);
    expect(result.current.progress).toBeLessThanOrEqual(100);
  });
});

describe('useIntelligentPreloader', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPreloadImages.mockResolvedValue(undefined);
  });

  it('should provide preloadImagesForPage function', () => {
    const { result } = renderHook(() => useIntelligentPreloader());
    
    expect(typeof result.current.preloadImagesForPage).toBe('function');
    expect(result.current.isPreloading).toBe(false);
  });

  it('should handle home page preloading', async () => {
    const { result } = renderHook(() => useIntelligentPreloader());
    
    const testMatches = [
      {
        id: '1',
        teams: {
          home: { badge: 'team1' },
          away: { badge: 'team2' }
        }
      }
    ];

    await act(async () => {
      await result.current.preloadImagesForPage('home', { matches: testMatches });
    });

    expect(mockPreloadImages).toHaveBeenCalled();
  });

  it('should handle sport page preloading', async () => {
    const { result } = renderHook(() => useIntelligentPreloader());
    
    const testMatches = [
      {
        id: '1',
        teams: {
          home: { badge: 'team1' },
          away: { badge: 'team2' }
        },
        poster: 'test-poster'
      }
    ];

    await act(async () => {
      await result.current.preloadImagesForPage('sport', { matches: testMatches });
    });

    expect(mockPreloadImages).toHaveBeenCalled();
  });

  it('should handle match page preloading', async () => {
    const { result } = renderHook(() => useIntelligentPreloader());
    
    const testMatch = {
      teams: {
        home: { badge: 'team1' },
        away: { badge: 'team2' }
      },
      poster: 'match-poster'
    };

    await act(async () => {
      await result.current.preloadImagesForPage('match', { match: testMatch });
    });

    expect(mockPreloadImages).toHaveBeenCalled();
  });

  it('should set preloading state correctly', async () => {
    const { result } = renderHook(() => useIntelligentPreloader());
    
    expect(result.current.isPreloading).toBe(false);
    
    const preloadPromise = act(async () => {
      await result.current.preloadImagesForPage('home', { matches: [] });
    });
    
    await preloadPromise;
    
    // Should return to false after completion
    expect(result.current.isPreloading).toBe(false);
  });

  it('should handle preloading errors gracefully', async () => {
    mockPreloadImages.mockRejectedValue(new Error('Preload failed'));
    
    const { result } = renderHook(() => useIntelligentPreloader());
    
    await act(async () => {
      await result.current.preloadImagesForPage('home', { matches: [{ id: '1' }] });
    });
    
    // Should not throw and should reset preloading state
    expect(result.current.isPreloading).toBe(false);
  });
});

describe('useImagePerformanceMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetCacheStats.mockReturnValue({
      totalCached: 10,
      loadedCount: 8,
      errorCount: 2,
      expiredCount: 0,
      queueLength: 3,
      isPreloading: false
    });
  });

  it('should return performance statistics', () => {
    const { result } = renderHook(() => useImagePerformanceMonitor());
    
    expect(result.current.totalLoaded).toBe(8);
    expect(result.current.totalFailed).toBe(2);
    expect(result.current.cacheHitRate).toBe(80); // 8/10 * 100
  });

  it('should handle zero cache scenario', () => {
    mockGetCacheStats.mockReturnValue({
      totalCached: 0,
      loadedCount: 0,
      errorCount: 0,
      expiredCount: 0,
      queueLength: 0,
      isPreloading: false
    });

    const { result } = renderHook(() => useImagePerformanceMonitor());
    
    expect(result.current.totalLoaded).toBe(0);
    expect(result.current.totalFailed).toBe(0);
    expect(result.current.cacheHitRate).toBe(0);
  });

  it('should update stats periodically', async () => {
    jest.useFakeTimers();
    
    const { result } = renderHook(() => useImagePerformanceMonitor());
    
    // Initial stats
    expect(result.current.totalLoaded).toBe(8);
    
    // Update mock to return different stats
    mockGetCacheStats.mockReturnValue({
      totalCached: 15,
      loadedCount: 12,
      errorCount: 3,
      expiredCount: 0,
      queueLength: 1,
      isPreloading: true
    });
    
    // Fast forward timer
    act(() => {
      jest.advanceTimersByTime(5000);
    });
    
    await waitFor(() => {
      expect(result.current.totalLoaded).toBe(12);
    });
    
    jest.useRealTimers();
  });
});
