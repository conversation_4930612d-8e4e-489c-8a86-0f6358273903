'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { streamedApiUtils } from '@/services/api';

// Image cache for better performance
const imageCache = new Map<string, { loaded: boolean; error: boolean; img?: HTMLImageElement }>();

// Preload image utility
const preloadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    // Check cache first
    const cached = imageCache.get(src);
    if (cached?.loaded && cached.img) {
      resolve(cached.img);
      return;
    }

    const img = new Image();
    img.onload = () => {
      imageCache.set(src, { loaded: true, error: false, img });
      resolve(img);
    };
    img.onerror = () => {
      imageCache.set(src, { loaded: false, error: true });
      reject(new Error(`Failed to load image: ${src}`));
    };
    img.src = src;
  });
};

// Intersection Observer for lazy loading
let intersectionObserver: IntersectionObserver | null = null;

const getIntersectionObserver = (): IntersectionObserver => {
  if (!intersectionObserver && typeof window !== 'undefined') {
    intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;
            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              intersectionObserver?.unobserve(img);
            }
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before image comes into view
        threshold: 0.1,
      }
    );
  }
  return intersectionObserver!;
};

interface OptimizedImageProps {
  src?: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  quality?: number; // Image quality (1-100)
  blur?: boolean; // Enable blur-to-sharp transition
  preload?: boolean; // Preload image in background
  retryCount?: number; // Number of retry attempts on failure
  onLoad?: () => void; // Callback when image loads
  onError?: (error: Error) => void; // Callback when image fails
}

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  fallbackSrc,
  loading = 'lazy',
  priority = false,
  quality = 85,
  blur = true,
  preload = false,
  retryCount = 2,
  onLoad,
  onError,
}: OptimizedImageProps) => {
  const [imageSrc, setImageSrc] = useState<string | null>(src || null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [isVisible, setIsVisible] = useState(priority || loading === 'eager');

  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!priority && loading === 'lazy' && containerRef.current) {
      const observer = getIntersectionObserver();
      const container = containerRef.current;

      const handleIntersection = (entries: IntersectionObserverEntry[]) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.target === container) {
            setIsVisible(true);
            observer.unobserve(container);
          }
        });
      };

      // Create a temporary observer for this specific element
      const tempObserver = new IntersectionObserver(handleIntersection, {
        rootMargin: '50px',
        threshold: 0.1,
      });

      tempObserver.observe(container);

      return () => {
        tempObserver.unobserve(container);
      };
    }
  }, [priority, loading]);

  // Handle src changes and preloading
  useEffect(() => {
    if (src && isVisible) {
      setImageSrc(src);
      setHasError(false);
      setIsLoading(true);
      setRetryAttempts(0);

      // Preload if enabled
      if (preload) {
        preloadImage(src).catch(() => {
          // Preload failed, but we'll still try to load normally
        });
      }
    }
  }, [src, isVisible, preload]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsLoading(false);

    // Try fallback first
    if (fallbackSrc && imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
      return;
    }

    // Try retry if attempts remaining
    if (retryAttempts < retryCount && imageSrc) {
      setRetryAttempts(prev => prev + 1);
      setIsLoading(true);

      // Retry after a short delay
      setTimeout(() => {
        if (imgRef.current) {
          imgRef.current.src = imageSrc;
        }
      }, 1000 * (retryAttempts + 1)); // Exponential backoff
      return;
    }

    // All retries exhausted
    setHasError(true);

    // Log error for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Failed to load image after ${retryAttempts} retries: ${imageSrc}`, {
        alt,
        src: imageSrc,
        retryAttempts
      });
    }

    const error = new Error(`Failed to load image: ${imageSrc}`);
    onError?.(error);
  }, [imageSrc, fallbackSrc, retryAttempts, retryCount, alt, onError]);

  // If not visible yet (lazy loading), show placeholder
  if (!isVisible || !imageSrc) {
    return (
      <div
        ref={containerRef}
        className={`bg-gray-800 border border-gray-700 rounded flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <svg
          className="w-6 h-6 text-gray-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`relative ${className}`} style={{ width, height }}>
      {/* Loading placeholder with blur effect */}
      {isLoading && (
        <div
          className={`absolute inset-0 bg-gray-800 border border-gray-700 rounded flex items-center justify-center ${
            blur ? 'animate-pulse' : ''
          }`}
          style={{ width, height }}
        >
          <svg
            className="w-6 h-6 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          {retryAttempts > 0 && (
            <div className="absolute bottom-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
              Retry {retryAttempts}/{retryCount}
            </div>
          )}
        </div>
      )}

      {/* Error placeholder */}
      {hasError && (
        <div
          className="absolute inset-0 bg-gray-800 border border-red-700 rounded flex flex-col items-center justify-center"
          style={{ width, height }}
        >
          <svg
            className="w-6 h-6 text-red-500 mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span className="text-red-400 text-xs">Failed to load</span>
        </div>
      )}

      {/* Actual image */}
      {!hasError && (
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : loading}
          onLoad={handleLoad}
          onError={handleError}
          className={`${
            isLoading ? 'opacity-0' : 'opacity-100'
          } transition-opacity duration-300 rounded object-contain ${
            blur && isLoading ? 'blur-sm' : ''
          }`}
          style={{
            width,
            height,
            filter: blur && isLoading ? 'blur(4px)' : 'none',
            transition: 'opacity 300ms ease-in-out, filter 300ms ease-in-out'
          }}
          decoding="async"
          fetchPriority={priority ? 'high' : 'auto'}
        />
      )}
    </div>
  );
};

// Specialized components for different image types

interface TeamBadgeProps {
  badgeId?: string;
  teamName: string;
  size?: number;
  className?: string;
}

export const TeamBadge = ({ badgeId, teamName, size = 32, className = '' }: TeamBadgeProps) => {
  // Use the more robust getImageUrl function that handles different badge formats
  const src = badgeId ? streamedApiUtils.getImageUrl(badgeId) : undefined;

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <OptimizedImage
        src={src}
        alt={`${teamName} badge`}
        width={size}
        height={size}
        className="rounded"
        loading="lazy"
        priority={size >= 64} // Prioritize large badges
        blur={true}
        retryCount={3}
        preload={size >= 48} // Preload medium and large badges
        quality={90} // High quality for badges
      />
      {/* Team name initial as fallback overlay for failed images */}
      {!src && (
        <div
          className="absolute inset-0 bg-gray-700 border border-gray-600 rounded flex items-center justify-center text-white font-bold"
          style={{ fontSize: `${size * 0.4}px` }}
        >
          {teamName.charAt(0).toUpperCase()}
        </div>
      )}
    </div>
  );
};

interface MatchPosterProps {
  homeBadge?: string;
  awayBadge?: string;
  poster?: string;
  matchTitle: string;
  homeTeam?: string;
  awayTeam?: string;
  className?: string;
  width?: number;
  height?: number;
  size?: 'small' | 'medium' | 'large' | 'hero';
  showFallback?: boolean;
}

export const MatchPoster = ({
  homeBadge,
  awayBadge,
  poster,
  matchTitle,
  homeTeam,
  awayTeam,
  className = '',
  width,
  height,
  size = 'medium',
  showFallback = true
}: MatchPosterProps) => {
  // Define size presets
  const sizePresets = {
    small: { width: 200, height: 120 },
    medium: { width: 300, height: 180 },
    large: { width: 400, height: 240 },
    hero: { width: 600, height: 360 }
  };

  const finalWidth = width || sizePresets[size].width;
  const finalHeight = height || sizePresets[size].height;

  let src: string | undefined;

  // Priority: poster > match poster from badges
  if (poster) {
    src = streamedApiUtils.getProxiedImageUrl(poster);
  } else if (homeBadge && awayBadge) {
    src = streamedApiUtils.getMatchPosterUrl(homeBadge, awayBadge);
  }

  // If no poster available and showFallback is false, return null
  if (!src && !showFallback) {
    return null;
  }

  // Custom fallback for match posters
  if (!src && showFallback && homeTeam && awayTeam) {
    return (
      <div
        className={`bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 rounded-lg flex flex-col items-center justify-center text-white ${className}`}
        style={{ width: finalWidth, height: finalHeight }}
      >
        <div className="flex items-center space-x-4 mb-2">
          <TeamBadge
            badgeId={homeBadge}
            teamName={homeTeam}
            size={size === 'small' ? 24 : size === 'medium' ? 32 : size === 'large' ? 40 : 48}
          />
          <span className="text-gray-400 font-bold text-lg">VS</span>
          <TeamBadge
            badgeId={awayBadge}
            teamName={awayTeam}
            size={size === 'small' ? 24 : size === 'medium' ? 32 : size === 'large' ? 40 : 48}
          />
        </div>
        <div className="text-center px-4">
          <h3 className={`font-semibold ${
            size === 'small' ? 'text-sm' :
            size === 'medium' ? 'text-base' :
            size === 'large' ? 'text-lg' : 'text-xl'
          }`}>
            {matchTitle}
          </h3>
          {homeTeam && awayTeam && (
            <p className={`text-gray-400 mt-1 ${
              size === 'small' ? 'text-xs' :
              size === 'medium' ? 'text-sm' : 'text-base'
            }`}>
              {homeTeam} vs {awayTeam}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={`${matchTitle} poster`}
      width={finalWidth}
      height={finalHeight}
      className={`rounded-lg ${className}`}
      loading={size === 'hero' ? 'eager' : 'lazy'}
      priority={size === 'hero'}
      blur={true}
      retryCount={2}
      preload={size === 'large' || size === 'hero'}
      quality={size === 'hero' ? 95 : 85}
    />
  );
};

// Image preloader utility
export const preloadImages = (urls: string[]): Promise<void[]> => {
  return Promise.all(urls.map(url => preloadImage(url).catch(() => {})));
};

// Responsive image component
interface ResponsiveImageProps extends Omit<OptimizedImageProps, 'src'> {
  srcSet: {
    src: string;
    width: number;
    height: number;
    breakpoint?: number; // Min width for this image
  }[];
  defaultSrc: string;
}

export const ResponsiveImage = ({ srcSet, defaultSrc, ...props }: ResponsiveImageProps) => {
  const [currentSrc, setCurrentSrc] = useState(defaultSrc);
  const [currentDimensions, setCurrentDimensions] = useState({
    width: props.width,
    height: props.height
  });

  useEffect(() => {
    const updateImage = () => {
      const windowWidth = window.innerWidth;

      // Find the best image for current screen size
      const sortedSrcSet = [...srcSet].sort((a, b) => (a.breakpoint || 0) - (b.breakpoint || 0));

      let selectedImage = sortedSrcSet[0];
      for (const image of sortedSrcSet) {
        if (!image.breakpoint || windowWidth >= image.breakpoint) {
          selectedImage = image;
        } else {
          break;
        }
      }

      setCurrentSrc(selectedImage.src);
      setCurrentDimensions({
        width: selectedImage.width,
        height: selectedImage.height
      });
    };

    updateImage();
    window.addEventListener('resize', updateImage);
    return () => window.removeEventListener('resize', updateImage);
  }, [srcSet]);

  return (
    <OptimizedImage
      {...props}
      src={currentSrc}
      width={currentDimensions.width}
      height={currentDimensions.height}
    />
  );
};

// Progressive image loading component
interface ProgressiveImageProps extends OptimizedImageProps {
  lowQualitySrc?: string; // Low quality placeholder
  highQualitySrc: string; // High quality final image
}

export const ProgressiveImage = ({
  lowQualitySrc,
  highQualitySrc,
  ...props
}: ProgressiveImageProps) => {
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || highQualitySrc);
  const [isHighQualityLoaded, setIsHighQualityLoaded] = useState(false);

  useEffect(() => {
    if (lowQualitySrc && highQualitySrc) {
      // Preload high quality image
      preloadImage(highQualitySrc)
        .then(() => {
          setCurrentSrc(highQualitySrc);
          setIsHighQualityLoaded(true);
        })
        .catch(() => {
          // Keep low quality if high quality fails
        });
    }
  }, [lowQualitySrc, highQualitySrc]);

  return (
    <OptimizedImage
      {...props}
      src={currentSrc}
      className={`${props.className || ''} ${
        !isHighQualityLoaded && lowQualitySrc ? 'filter blur-sm' : ''
      }`}
      blur={!isHighQualityLoaded}
    />
  );
};

export default OptimizedImage;
