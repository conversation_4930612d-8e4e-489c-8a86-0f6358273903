/**
 * Image Optimization Service
 * Provides advanced image loading, caching, and optimization features
 */

// Image cache with metadata
interface CachedImage {
  url: string;
  loaded: boolean;
  error: boolean;
  timestamp: number;
  size?: { width: number; height: number };
  blob?: Blob;
}

class ImageOptimizationService {
  private cache = new Map<string, CachedImage>();
  private preloadQueue: string[] = [];
  private isPreloading = false;
  private maxCacheSize = 100; // Maximum number of cached images
  private cacheExpiry = 30 * 60 * 1000; // 30 minutes

  /**
   * Preload an image and cache it
   */
  async preloadImage(url: string): Promise<HTMLImageElement> {
    // Check cache first
    const cached = this.cache.get(url);
    if (cached?.loaded && !this.isCacheExpired(cached)) {
      return this.createImageFromCache(cached);
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        this.cacheImage(url, {
          url,
          loaded: true,
          error: false,
          timestamp: Date.now(),
          size: { width: img.naturalWidth, height: img.naturalHeight }
        });
        resolve(img);
      };

      img.onerror = () => {
        this.cacheImage(url, {
          url,
          loaded: false,
          error: true,
          timestamp: Date.now()
        });
        reject(new Error(`Failed to preload image: ${url}`));
      };

      img.src = url;
    });
  }

  /**
   * Preload multiple images in batches
   */
  async preloadImages(urls: string[], batchSize = 3): Promise<void> {
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      await Promise.allSettled(
        batch.map(url => this.preloadImage(url))
      );
      
      // Small delay between batches to avoid overwhelming the browser
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  /**
   * Add images to preload queue
   */
  queuePreload(urls: string[]): void {
    this.preloadQueue.push(...urls.filter(url => !this.cache.has(url)));
    this.processPreloadQueue();
  }

  /**
   * Process the preload queue
   */
  private async processPreloadQueue(): Promise<void> {
    if (this.isPreloading || this.preloadQueue.length === 0) {
      return;
    }

    this.isPreloading = true;
    
    try {
      const batch = this.preloadQueue.splice(0, 5); // Process 5 at a time
      await this.preloadImages(batch);
    } catch (error) {
      console.warn('Error processing preload queue:', error);
    } finally {
      this.isPreloading = false;
      
      // Continue processing if there are more items
      if (this.preloadQueue.length > 0) {
        setTimeout(() => this.processPreloadQueue(), 500);
      }
    }
  }

  /**
   * Get image from cache
   */
  getCachedImage(url: string): CachedImage | null {
    const cached = this.cache.get(url);
    if (cached && !this.isCacheExpired(cached)) {
      return cached;
    }
    
    // Remove expired cache entry
    if (cached) {
      this.cache.delete(url);
    }
    
    return null;
  }

  /**
   * Cache an image
   */
  private cacheImage(url: string, imageData: CachedImage): void {
    // Clean up old cache entries if we're at the limit
    if (this.cache.size >= this.maxCacheSize) {
      this.cleanupCache();
    }

    this.cache.set(url, imageData);
  }

  /**
   * Check if cache entry is expired
   */
  private isCacheExpired(cached: CachedImage): boolean {
    return Date.now() - cached.timestamp > this.cacheExpiry;
  }

  /**
   * Create image element from cached data
   */
  private createImageFromCache(cached: CachedImage): HTMLImageElement {
    const img = new Image();
    img.src = cached.url;
    if (cached.size) {
      img.width = cached.size.width;
      img.height = cached.size.height;
    }
    return img;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    const entriesToDelete: string[] = [];

    this.cache.forEach((cached, url) => {
      if (this.isCacheExpired(cached)) {
        entriesToDelete.push(url);
      }
    });

    // If still too many entries, remove oldest ones
    if (this.cache.size - entriesToDelete.length >= this.maxCacheSize) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp);
      
      const additionalToDelete = sortedEntries
        .slice(0, this.cache.size - this.maxCacheSize + 10) // Remove extra 10 for buffer
        .map(([url]) => url);
      
      entriesToDelete.push(...additionalToDelete);
    }

    entriesToDelete.forEach(url => this.cache.delete(url));
  }

  /**
   * Clear all cached images
   */
  clearCache(): void {
    this.cache.clear();
    this.preloadQueue.length = 0;
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const now = Date.now();
    let loadedCount = 0;
    let errorCount = 0;
    let expiredCount = 0;

    this.cache.forEach(cached => {
      if (this.isCacheExpired(cached)) {
        expiredCount++;
      } else if (cached.loaded) {
        loadedCount++;
      } else if (cached.error) {
        errorCount++;
      }
    });

    return {
      totalCached: this.cache.size,
      loadedCount,
      errorCount,
      expiredCount,
      queueLength: this.preloadQueue.length,
      isPreloading: this.isPreloading
    };
  }

  /**
   * Generate optimized image URL with quality and format parameters
   */
  getOptimizedUrl(baseUrl: string, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  } = {}): string {
    // For Streamed API, we already get WebP format
    // This method can be extended for other image services
    return baseUrl;
  }

  /**
   * Detect if WebP is supported
   */
  isWebPSupported(): boolean {
    if (typeof window === 'undefined') return true; // Assume supported on server
    
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  /**
   * Get device pixel ratio for high-DPI displays
   */
  getDevicePixelRatio(): number {
    return typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
  }

  /**
   * Calculate optimal image dimensions for current device
   */
  getOptimalDimensions(baseWidth: number, baseHeight: number): {
    width: number;
    height: number;
  } {
    const pixelRatio = this.getDevicePixelRatio();
    
    return {
      width: Math.round(baseWidth * pixelRatio),
      height: Math.round(baseHeight * pixelRatio)
    };
  }
}

// Export singleton instance
export const imageOptimizationService = new ImageOptimizationService();

// Export utility functions
export const preloadImages = (urls: string[]) => 
  imageOptimizationService.preloadImages(urls);

export const queueImagePreload = (urls: string[]) => 
  imageOptimizationService.queuePreload(urls);

export const getCachedImage = (url: string) => 
  imageOptimizationService.getCachedImage(url);

export const clearImageCache = () => 
  imageOptimizationService.clearCache();

export const getImageCacheStats = () => 
  imageOptimizationService.getCacheStats();
