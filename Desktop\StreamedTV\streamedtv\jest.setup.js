import '@testing-library/jest-dom'

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock canvas for WebP detection
HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/webp;base64,test')

// Mock Image constructor
global.Image = class {
  constructor() {
    setTimeout(() => {
      if (this.src && !this.src.includes('invalid') && !this.src.includes('error')) {
        this.onload && this.onload()
      } else {
        this.onerror && this.onerror()
      }
    }, 10)
  }
}

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    blob: () => Promise.resolve(new Blob(['test'], { type: 'image/webp' })),
  })
)

// Suppress console warnings in tests
const originalWarn = console.warn
beforeEach(() => {
  console.warn = jest.fn()
})

afterEach(() => {
  console.warn = originalWarn
})
