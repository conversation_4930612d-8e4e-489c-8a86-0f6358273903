'use client';

import { useState, useEffect } from 'react';
import { Stream } from '@/types/api';
import {
  getBestStream,
  sortStreamsByPreference,
  getUserStreamPreferences,
  getAvailableLanguages,
  getStreamStats
} from '@/utils/streamUtils';

interface StreamPlayerProps {
  title: string;
  streams: Stream[];
  onStreamSelect?: (stream: Stream) => void;
}

const StreamPlayer = ({ title, streams, onStreamSelect }: StreamPlayerProps) => {
  const [selectedStream, setSelectedStream] = useState<Stream | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [sortedStreams, setSortedStreams] = useState<Stream[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [selectedQuality, setSelectedQuality] = useState<string>('all');

  // Initialize streams and select the best one
  useEffect(() => {
    if (streams.length > 0) {
      const preferences = getUserStreamPreferences();
      const sorted = sortStreamsByPreference(streams, preferences);
      setSortedStreams(sorted);

      // Auto-select the best stream if none is selected
      if (!selectedStream) {
        const bestStream = getBestStream(streams, preferences);
        if (bestStream) {
          setSelectedStream(bestStream);
          onStreamSelect?.(bestStream);
        }
      }
    } else {
      setSortedStreams([]);
      setSelectedStream(null);
    }
  }, [streams, selectedStream, onStreamSelect]);

  const handleStreamSelect = (stream: Stream) => {
    setSelectedStream(stream);
    onStreamSelect?.(stream);
  };

  // Get available languages and stats
  const availableLanguages = getAvailableLanguages(streams);
  const streamStats = getStreamStats(streams);

  // Filter streams based on selected filters
  const filteredStreams = sortedStreams.filter(stream => {
    if (selectedLanguage && stream.language !== selectedLanguage) return false;
    if (selectedQuality === 'hd' && !stream.hd) return false;
    if (selectedQuality === 'sd' && stream.hd) return false;
    return true;
  });

  const PlayIcon = () => (
    <svg className="h-16 w-16" fill="currentColor" viewBox="0 0 24 24">
      <path d="M8 5v14l11-7z"/>
    </svg>
  );

  const FullscreenIcon = () => (
    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 1v4m0 0h-4m4 0l-5-5" />
    </svg>
  );

  const SettingsIcon = () => (
    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  );

  return (
    <div className="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden">
      {/* Stream Title */}
      <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
        <h2 className="text-xl font-bold text-white">{title}</h2>
      </div>

      {/* Video Player Area */}
      <div className="relative bg-black aspect-video">
        {selectedStream && selectedStream.embedUrl ? (
          <iframe
            src={selectedStream.embedUrl}
            className="w-full h-full"
            frameBorder="0"
            allowFullScreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            title={`Stream ${selectedStream.streamNo} - ${selectedStream.language}`}
          />
        ) : isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-gray-400 text-lg mb-2">Loading stream...</p>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <PlayIcon />
              </div>
              <p className="text-gray-400 text-lg mb-2">
                {streams.length > 0 ? 'Select a stream to start watching' : 'No streams available'}
              </p>
              {streams.length > 0 && (
                <p className="text-gray-500 text-sm">
                  Choose from {streams.length} available stream{streams.length !== 1 ? 's' : ''} below
                </p>
              )}
            </div>
          </div>
        )}

        {/* Player Controls Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="text-white hover:text-gray-300 transition-colors">
                <PlayIcon />
              </button>
              <div className="text-white text-sm">
                <span>00:00 / 00:00</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="text-white hover:text-gray-300 transition-colors p-2">
                <SettingsIcon />
              </button>
              <button 
                className="text-white hover:text-gray-300 transition-colors p-2"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                <FullscreenIcon />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stream Options */}
      <div className="p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h3 className="text-lg font-semibold text-white mb-4 md:mb-0">
            Available Streams ({streamStats.total})
          </h3>

          {/* Stream Stats */}
          <div className="flex items-center space-x-4 text-sm text-gray-400">
            <span>{streamStats.hdCount} HD</span>
            <span>•</span>
            <span>{streamStats.sdCount} SD</span>
            <span>•</span>
            <span>{streamStats.languages} Languages</span>
            <span>•</span>
            <span>{streamStats.sources} Sources</span>
          </div>
        </div>

        {streams.length > 0 ? (
          <>
            {/* Filters */}
            <div className="flex flex-wrap gap-4 mb-6">
              {/* Language Filter */}
              {availableLanguages.length > 1 && (
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Languages</option>
                  {availableLanguages.map(lang => (
                    <option key={lang} value={lang}>{lang}</option>
                  ))}
                </select>
              )}

              {/* Quality Filter */}
              {streamStats.hdCount > 0 && streamStats.sdCount > 0 && (
                <select
                  value={selectedQuality}
                  onChange={(e) => setSelectedQuality(e.target.value)}
                  className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Quality</option>
                  <option value="hd">HD Only</option>
                  <option value="sd">SD Only</option>
                </select>
              )}
            </div>

            {/* Stream Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {filteredStreams.map((stream) => (
                <button
                  key={stream.id}
                  onClick={() => handleStreamSelect(stream)}
                  className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                    selectedStream?.id === stream.id
                      ? 'bg-blue-600 border-blue-500 text-white'
                      : 'bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700 hover:border-gray-600'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <span className="font-medium">
                      Stream #{stream.streamNo}
                    </span>
                    <div className="flex items-center space-x-1">
                      {stream.hd && (
                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                          HD
                        </span>
                      )}
                      <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                        {stream.source.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="text-sm opacity-75">
                    <div>Quality: {stream.hd ? 'HD' : 'SD'}</div>
                    <div>Language: {stream.language}</div>
                    <div>Source: {stream.source}</div>
                  </div>
                </button>
              ))}
            </div>

            {filteredStreams.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-400 mb-2">No streams match your filters</p>
                <button
                  onClick={() => {
                    setSelectedLanguage('');
                    setSelectedQuality('all');
                  }}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  Clear filters
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-400 mb-2">No streams available</p>
            <p className="text-gray-500 text-sm">
              This match may not have started yet or streams may not be available.
            </p>
          </div>
        )}
      </div>

      {/* Stream Info */}
      <div className="bg-gray-800 px-6 py-4 border-t border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="mb-4 md:mb-0">
            {selectedStream ? (
              <p className="text-gray-400 text-sm">
                <span className="text-green-400">●</span>
                {selectedStream.language} Stream #{selectedStream.streamNo} Active
              </p>
            ) : (
              <p className="text-gray-400 text-sm">
                <span className="text-gray-500">○</span> No stream selected
              </p>
            )}
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-400">
            {selectedStream && (
              <>
                <span>Quality: {selectedStream.hd ? 'HD' : 'SD'}</span>
                <span>•</span>
                <span>Source: {selectedStream.source.toUpperCase()}</span>
                <span>•</span>
                <span>Language: {selectedStream.language}</span>
              </>
            )}
            {streams.length > 0 && (
              <>
                {selectedStream && <span>•</span>}
                <span>{streams.length} stream{streams.length !== 1 ? 's' : ''} available</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StreamPlayer;
