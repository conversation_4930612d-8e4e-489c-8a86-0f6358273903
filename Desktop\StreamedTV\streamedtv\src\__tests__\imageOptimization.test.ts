/**
 * Image Optimization Service Tests
 * Tests for caching, preloading, and optimization functionality
 */

import { imageOptimizationService } from '@/services/imageOptimization';

// Mock Image constructor for testing
class MockImage {
  src: string = '';
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  naturalWidth: number = 100;
  naturalHeight: number = 100;

  constructor() {
    // Simulate async loading
    setTimeout(() => {
      if (this.src.includes('invalid') || this.src.includes('error')) {
        this.onerror?.();
      } else {
        this.onload?.();
      }
    }, 10);
  }
}

// Mock global Image
(global as any).Image = MockImage;

describe('ImageOptimizationService', () => {
  beforeEach(() => {
    // Clear cache before each test
    imageOptimizationService.clearCache();
  });

  describe('preloadImage', () => {
    it('should successfully preload a valid image', async () => {
      const url = 'https://example.com/valid-image.jpg';
      const img = await imageOptimizationService.preloadImage(url);
      
      expect(img).toBeInstanceOf(MockImage);
      expect(img.src).toBe(url);
    });

    it('should reject when preloading an invalid image', async () => {
      const url = 'https://example.com/invalid-image.jpg';
      
      await expect(imageOptimizationService.preloadImage(url))
        .rejects
        .toThrow('Failed to preload image');
    });

    it('should cache successfully loaded images', async () => {
      const url = 'https://example.com/cached-image.jpg';
      await imageOptimizationService.preloadImage(url);
      
      const cached = imageOptimizationService.getCachedImage(url);
      expect(cached).toBeTruthy();
      expect(cached?.loaded).toBe(true);
      expect(cached?.error).toBe(false);
    });

    it('should cache failed image attempts', async () => {
      const url = 'https://example.com/error-image.jpg';
      
      try {
        await imageOptimizationService.preloadImage(url);
      } catch {
        // Expected to fail
      }
      
      const cached = imageOptimizationService.getCachedImage(url);
      expect(cached).toBeTruthy();
      expect(cached?.loaded).toBe(false);
      expect(cached?.error).toBe(true);
    });
  });

  describe('preloadImages', () => {
    it('should preload multiple images in batches', async () => {
      const urls = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
        'https://example.com/image3.jpg',
        'https://example.com/image4.jpg',
        'https://example.com/image5.jpg'
      ];

      await imageOptimizationService.preloadImages(urls, 2);
      
      // Check that all images are cached
      urls.forEach(url => {
        const cached = imageOptimizationService.getCachedImage(url);
        expect(cached).toBeTruthy();
        expect(cached?.loaded).toBe(true);
      });
    });

    it('should handle mixed success/failure in batch preloading', async () => {
      const urls = [
        'https://example.com/valid1.jpg',
        'https://example.com/invalid1.jpg',
        'https://example.com/valid2.jpg',
        'https://example.com/error2.jpg'
      ];

      await imageOptimizationService.preloadImages(urls, 2);
      
      // Check valid images are cached as loaded
      const valid1 = imageOptimizationService.getCachedImage(urls[0]);
      const valid2 = imageOptimizationService.getCachedImage(urls[2]);
      expect(valid1?.loaded).toBe(true);
      expect(valid2?.loaded).toBe(true);
      
      // Check invalid images are cached as errors
      const invalid1 = imageOptimizationService.getCachedImage(urls[1]);
      const error2 = imageOptimizationService.getCachedImage(urls[3]);
      expect(invalid1?.error).toBe(true);
      expect(error2?.error).toBe(true);
    });
  });

  describe('queuePreload', () => {
    it('should add images to preload queue', () => {
      const urls = [
        'https://example.com/queue1.jpg',
        'https://example.com/queue2.jpg'
      ];

      imageOptimizationService.queuePreload(urls);
      
      // Queue processing is async, so we check the queue was created
      const stats = imageOptimizationService.getCacheStats();
      expect(stats.queueLength).toBeGreaterThan(0);
    });

    it('should not queue already cached images', async () => {
      const url = 'https://example.com/already-cached.jpg';
      
      // Preload first
      await imageOptimizationService.preloadImage(url);
      
      // Try to queue the same image
      imageOptimizationService.queuePreload([url]);
      
      // Should not be added to queue since it's already cached
      const stats = imageOptimizationService.getCacheStats();
      expect(stats.queueLength).toBe(0);
    });
  });

  describe('getCachedImage', () => {
    it('should return cached image data', async () => {
      const url = 'https://example.com/test-cache.jpg';
      await imageOptimizationService.preloadImage(url);
      
      const cached = imageOptimizationService.getCachedImage(url);
      expect(cached).toBeTruthy();
      expect(cached?.url).toBe(url);
      expect(cached?.loaded).toBe(true);
      expect(cached?.timestamp).toBeGreaterThan(0);
    });

    it('should return null for non-cached images', () => {
      const url = 'https://example.com/not-cached.jpg';
      const cached = imageOptimizationService.getCachedImage(url);
      expect(cached).toBeNull();
    });

    it('should return null for expired cache entries', async () => {
      const url = 'https://example.com/expired.jpg';
      await imageOptimizationService.preloadImage(url);
      
      // Mock expired timestamp
      const cache = (imageOptimizationService as any).cache;
      const entry = cache.get(url);
      entry.timestamp = Date.now() - (31 * 60 * 1000); // 31 minutes ago
      
      const cached = imageOptimizationService.getCachedImage(url);
      expect(cached).toBeNull();
    });
  });

  describe('clearCache', () => {
    it('should clear all cached images', async () => {
      const urls = [
        'https://example.com/clear1.jpg',
        'https://example.com/clear2.jpg'
      ];

      await imageOptimizationService.preloadImages(urls);
      
      let stats = imageOptimizationService.getCacheStats();
      expect(stats.totalCached).toBeGreaterThan(0);
      
      imageOptimizationService.clearCache();
      
      stats = imageOptimizationService.getCacheStats();
      expect(stats.totalCached).toBe(0);
      expect(stats.queueLength).toBe(0);
    });
  });

  describe('getCacheStats', () => {
    it('should return accurate cache statistics', async () => {
      const validUrls = [
        'https://example.com/stats1.jpg',
        'https://example.com/stats2.jpg'
      ];
      const invalidUrls = [
        'https://example.com/invalid-stats1.jpg',
        'https://example.com/error-stats2.jpg'
      ];

      await imageOptimizationService.preloadImages([...validUrls, ...invalidUrls]);
      
      const stats = imageOptimizationService.getCacheStats();
      expect(stats.totalCached).toBe(4);
      expect(stats.loadedCount).toBe(2);
      expect(stats.errorCount).toBe(2);
      expect(stats.expiredCount).toBe(0);
    });
  });

  describe('getOptimalDimensions', () => {
    it('should calculate optimal dimensions for high-DPI displays', () => {
      // Mock device pixel ratio
      Object.defineProperty(window, 'devicePixelRatio', {
        writable: true,
        value: 2
      });

      const optimal = imageOptimizationService.getOptimalDimensions(100, 100);
      expect(optimal.width).toBe(200);
      expect(optimal.height).toBe(200);
    });

    it('should handle standard displays', () => {
      Object.defineProperty(window, 'devicePixelRatio', {
        writable: true,
        value: 1
      });

      const optimal = imageOptimizationService.getOptimalDimensions(150, 100);
      expect(optimal.width).toBe(150);
      expect(optimal.height).toBe(100);
    });
  });

  describe('isWebPSupported', () => {
    it('should detect WebP support', () => {
      // Mock canvas and toDataURL
      const mockCanvas = {
        width: 1,
        height: 1,
        toDataURL: jest.fn().mockReturnValue('data:image/webp;base64,test')
      };
      
      document.createElement = jest.fn().mockReturnValue(mockCanvas);
      
      const isSupported = imageOptimizationService.isWebPSupported();
      expect(isSupported).toBe(true);
    });

    it('should handle lack of WebP support', () => {
      const mockCanvas = {
        width: 1,
        height: 1,
        toDataURL: jest.fn().mockReturnValue('data:image/png;base64,test')
      };
      
      document.createElement = jest.fn().mockReturnValue(mockCanvas);
      
      const isSupported = imageOptimizationService.isWebPSupported();
      expect(isSupported).toBe(false);
    });
  });

  describe('cache management', () => {
    it('should enforce cache size limits', async () => {
      // Set a small cache limit for testing
      const originalMaxSize = (imageOptimizationService as any).maxCacheSize;
      (imageOptimizationService as any).maxCacheSize = 3;

      const urls = [
        'https://example.com/limit1.jpg',
        'https://example.com/limit2.jpg',
        'https://example.com/limit3.jpg',
        'https://example.com/limit4.jpg',
        'https://example.com/limit5.jpg'
      ];

      await imageOptimizationService.preloadImages(urls);
      
      const stats = imageOptimizationService.getCacheStats();
      expect(stats.totalCached).toBeLessThanOrEqual(3);

      // Restore original cache size
      (imageOptimizationService as any).maxCacheSize = originalMaxSize;
    });

    it('should clean up expired entries', async () => {
      const url = 'https://example.com/expire-test.jpg';
      await imageOptimizationService.preloadImage(url);
      
      // Mock expired entry
      const cache = (imageOptimizationService as any).cache;
      const entry = cache.get(url);
      entry.timestamp = Date.now() - (35 * 60 * 1000); // 35 minutes ago
      
      // Trigger cleanup by adding new image
      await imageOptimizationService.preloadImage('https://example.com/new.jpg');
      
      // Expired entry should be cleaned up
      const expiredCached = imageOptimizationService.getCachedImage(url);
      expect(expiredCached).toBeNull();
    });
  });
});
