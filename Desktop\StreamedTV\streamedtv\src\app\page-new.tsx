'use client';

import React from 'react';
import Link from 'next/link';
import { useSports, useAllMatches, useLiveMatches, useTodayMatches } from '@/hooks/useApi';
import { APIMatch } from '@/types/api';
import { streamedApiUtils } from '@/services/api';
import { TeamBadge } from '@/components/OptimizedImage';
import { useMatchImagePreloader, useIntelligentPreloader } from '@/hooks/useOptimizedImages';

// Helper function to extract team names
function getTeamNames(match: APIMatch) {
  const homeTeam = match.teams?.home?.name || match.title.split(' vs ')[0] || match.title.split(' v ')[0] || 'Home';
  const awayTeam = match.teams?.away?.name || match.title.split(' vs ')[1] || match.title.split(' v ')[1] || 'Away';
  return { homeTeam, awayTeam };
}

// Helper function to check if match is live
function isMatchLive(match: APIMatch) {
  const now = Date.now();
  const matchTime = match.date;
  const timeDiff = now - matchTime;
  return timeDiff >= 0 && timeDiff <= 3 * 60 * 60 * 1000; // Live if within 3 hours of start time
}

// Sport category component
function SportSection({ 
  title, 
  matches, 
  icon, 
  color = 'green' 
}: { 
  title: string; 
  matches: APIMatch[]; 
  icon: string; 
  color?: string;
}) {
  if (!matches || matches.length === 0) return null;

  const colorClasses = {
    green: 'bg-green-600',
    orange: 'bg-orange-600', 
    purple: 'bg-purple-600',
    blue: 'bg-blue-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600',
    pink: 'bg-pink-600',
    indigo: 'bg-indigo-600'
  };

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-bold text-white mb-6 border-b border-gray-800 pb-2">
        {title}
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {matches.slice(0, 8).map((match) => {
          const { homeTeam, awayTeam } = getTeamNames(match);
          const isLive = isMatchLive(match);
          
          return (
            <Link
              key={match.id}
              href={`/match/${match.id}`}
              className="bg-gray-900 border border-gray-800 rounded-lg p-4 hover:bg-gray-800 hover:border-gray-700 transition-all group"
            >
              <div className="flex items-center justify-between mb-2">
                <span className={`${isLive ? 'bg-red-600' : colorClasses[color as keyof typeof colorClasses]} text-white text-xs px-2 py-1 rounded font-medium`}>
                  {isLive ? 'LIVE' : 'POPULAR'}
                </span>
                <span className="text-gray-400 text-xs">{icon} {match.category}</span>
              </div>
              
              <div className="flex items-center space-x-3 mb-3">
                <TeamBadge
                  badgeId={match.teams?.home?.badge}
                  teamName={homeTeam}
                  size={24}
                />
                <span className="text-gray-400 text-sm">vs</span>
                <TeamBadge
                  badgeId={match.teams?.away?.badge}
                  teamName={awayTeam}
                  size={24}
                />
              </div>
              
              <h3 className="text-white font-medium text-sm mb-2 group-hover:text-blue-400 transition-colors line-clamp-2">
                {match.title}
              </h3>
              <div className="text-gray-500 text-xs">
                {streamedApiUtils.formatMatchTime(match.date)}
              </div>
            </Link>
          );
        })}
      </div>
    </section>
  );
}

export default function Home() {
  const { data: allMatches, loading: matchesLoading } = useAllMatches();
  const { data: liveMatches, loading: liveLoading } = useLiveMatches();

  // Optimized image loading
  const { preloadImagesForPage } = useIntelligentPreloader();

  // Get popular matches by sport
  const getPopularMatchesBySport = (sport: string, limit: number = 8) => {
    return allMatches?.filter(match => 
      match.category.toLowerCase().includes(sport.toLowerCase()) && match.popular
    ).slice(0, limit) || [];
  };

  // Get all matches by sport (fallback if no popular matches)
  const getMatchesBySport = (sport: string, limit: number = 8) => {
    const popular = getPopularMatchesBySport(sport, limit);
    if (popular.length > 0) return popular;
    
    return allMatches?.filter(match => 
      match.category.toLowerCase().includes(sport.toLowerCase())
    ).slice(0, limit) || [];
  };

  // Preload images when data is available
  React.useEffect(() => {
    if (liveMatches && liveMatches.length > 0) {
      preloadImagesForPage('home', { matches: liveMatches });
    }
  }, [liveMatches, preloadImagesForPage]);

  return (
    <div className="min-h-screen bg-black">
      <div className="container mx-auto px-4 py-8">
        {/* Important Notice */}
        <div className="bg-blue-900 border border-blue-700 rounded-lg p-4 mb-8">
          <p className="text-blue-100 text-center">
            <strong>Important:</strong> Please use the strmd.link web address instead of Google to access the original Streamed URLs.{' '}
            <Link href="https://strmd.link" className="text-blue-300 hover:text-blue-200 underline" target="_blank" rel="noopener noreferrer">
              Click here to explore our mirror websites.
            </Link>
          </p>
        </div>

        {/* Sports Categories */}
        <div className="space-y-0">
          {/* Popular Live */}
          <SportSection
            title="Popular Live"
            matches={liveMatches || []}
            icon="🔴"
            color="red"
          />

          {/* Popular Football */}
          <SportSection
            title="Popular Football"
            matches={getMatchesBySport('football')}
            icon="⚽"
            color="green"
          />

          {/* Popular Basketball */}
          <SportSection
            title="Popular Basketball"
            matches={getMatchesBySport('basketball')}
            icon="🏀"
            color="orange"
          />

          {/* Popular American Football */}
          <SportSection
            title="Popular American Football"
            matches={getMatchesBySport('american-football')}
            icon="🏈"
            color="purple"
          />

          {/* Popular Hockey */}
          <SportSection
            title="Popular Hockey"
            matches={getMatchesBySport('hockey')}
            icon="🏒"
            color="blue"
          />

          {/* Popular Baseball */}
          <SportSection
            title="Popular Baseball"
            matches={getMatchesBySport('baseball')}
            icon="⚾"
            color="yellow"
          />

          {/* Popular Fighting */}
          <SportSection
            title="Popular Fighting"
            matches={getMatchesBySport('fighting')}
            icon="🥊"
            color="pink"
          />

          {/* Popular Motorsports */}
          <SportSection
            title="Popular Motorsports"
            matches={getMatchesBySport('motorsports')}
            icon="🏎️"
            color="indigo"
          />
        </div>

        {/* Footer Info */}
        <div className="mt-16 text-center border-t border-gray-800 pt-8">
          <p className="text-gray-400 mb-4">
            Join our Discord community for updates and support
          </p>
          <Link 
            href="https://discord.gg/streamed" 
            target="_blank"
            className="inline-flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
            </svg>
            <span>Join Discord</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
