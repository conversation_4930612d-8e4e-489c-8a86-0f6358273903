# StreamedTV - Sports Streaming Web Application

A modern, responsive sports streaming web application built with Next.js, TypeScript, and Tailwind CSS. Features a dark theme design inspired by streamed.pk with comprehensive sports streaming functionality.

## 🚀 Features

- **Modern Dark Theme**: Clean, responsive design with a dark theme aesthetic
- **Sports Categories**: Organized sports content including NBA, NFL, NHL, MLB, UFC, and more
- **Streaming Interface**: Professional video player interface with stream selection
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **API Ready**: Complete API integration structure for real-time sports data
- **Real-time Updates**: WebSocket support for live game updates
- **Search Functionality**: Advanced search with debounced queries
- **User Preferences**: Customizable user settings and favorites

## 🛠️ Technology Stack

- **Framework**: Next.js 15.5.0 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **Icons**: Custom SVG icons
- **State Management**: React Hooks
- **API Client**: Custom fetch-based client with error handling
- **Real-time**: WebSocket integration ready

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd streamedtv
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables (optional):
```bash
cp .env.example .env.local
```

Add your API configuration:
```env
NEXT_PUBLIC_API_BASE_URL=https://api.streamed.tv
NEXT_PUBLIC_WS_URL=wss://ws.streamed.tv
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── globals.css        # Global styles and theme
│   ├── layout.tsx         # Root layout with header/footer
│   ├── page.tsx           # Homepage with sports categories
│   └── nba/               # Example sport page
├── components/            # Reusable UI components
│   ├── Header.tsx         # Navigation header
│   ├── Footer.tsx         # Site footer
│   └── StreamPlayer.tsx   # Video streaming interface
├── hooks/                 # Custom React hooks
│   └── useApi.ts          # API data fetching hooks
├── services/              # API and external services
│   └── api.ts             # API client and utilities
└── types/                 # TypeScript type definitions
    └── api.ts             # API response types
```

## 🎨 Design Features

- **Dark Theme**: Deep black/gray backgrounds with bright accents
- **Responsive Grid**: Adaptive layouts for all screen sizes
- **Modern Typography**: Clean, readable fonts with proper hierarchy
- **Interactive Elements**: Hover effects and smooth transitions
- **Accessibility**: Proper contrast ratios and semantic HTML

## 🔌 API Integration

The application is structured to integrate with a sports streaming API. Key features include:

- **Type-safe API client** with error handling and retries
- **React hooks** for data fetching with loading states
- **Real-time updates** via WebSocket connections
- **Caching and optimization** for better performance
- **Search functionality** with debounced queries

### API Endpoints Structure

```typescript
// Sports and games
GET /sports                 # List all sports
GET /sports/{slug}          # Get specific sport
GET /games                  # List games with filters
GET /games/{id}             # Get specific game
GET /games/{id}/streams     # Get streams for game

// Schedule and categories
GET /schedule               # Get schedule with filters
GET /categories             # List stream categories
GET /search                 # Search games/teams/sports
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables
4. Deploy automatically

### Other Platforms

```bash
# Build for production
npm run build

# Start production server
npm start
```

## 🧪 Testing

The application includes comprehensive error handling and loading states. To test:

1. **Homepage**: Navigate through sports categories
2. **Streaming Page**: Visit `/nba` to see the streaming interface
3. **Responsive Design**: Test on different screen sizes
4. **Navigation**: Use the header navigation and search

## 🔧 Configuration

### Environment Variables

- `NEXT_PUBLIC_API_BASE_URL`: API base URL
- `NEXT_PUBLIC_WS_URL`: WebSocket URL for real-time updates

### Customization

- **Colors**: Modify CSS variables in `globals.css`
- **Sports Categories**: Update data in `page.tsx`
- **API Endpoints**: Configure in `services/api.ts`

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the code comments
- Open an issue on GitHub

---

Built with ❤️ using Next.js and modern web technologies.
