'use client';

import { useState, useEffect, useCallback } from 'react';
import { streamedApi, streamedApiUtils } from '@/services/api';
import { Sport, APIMatch, Stream, StreamedMatch, StreamData } from '@/types/api';

// Generic hook for API requests
export function useApiRequest<T>(
  requestFn: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await requestFn();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Hook for fetching sports categories
export function useSports() {
  return useApiRequest(async () => {
    return await streamedApi.getSports();
  });
}

// Hook for fetching matches by sport
export function useMatches(sport?: string) {
  return useApiRequest(
    async () => {
      if (!sport) return [];
      return await streamedApi.getMatches(sport);
    },
    [sport]
  );
}

// Hook for fetching popular matches by sport
export function usePopularMatches(sport?: string) {
  return useApiRequest(
    async () => {
      if (!sport) return [];
      return await streamedApi.getPopularMatches(sport);
    },
    [sport]
  );
}

// Hook for fetching all matches
export function useAllMatches() {
  return useApiRequest(async () => {
    return await streamedApi.getAllMatches();
  });
}

// Hook for fetching all popular matches
export function useAllPopularMatches() {
  return useApiRequest(async () => {
    return await streamedApi.getAllPopularMatches();
  });
}

// Hook for fetching live matches
export function useLiveMatches() {
  return useApiRequest(async () => {
    return await streamedApi.getLiveMatches();
  });
}

// Hook for fetching popular live matches
export function usePopularLiveMatches() {
  return useApiRequest(async () => {
    return await streamedApi.getLivePopularMatches();
  });
}

// Hook for fetching today's matches
export function useTodayMatches() {
  return useApiRequest(async () => {
    return await streamedApi.getTodayMatches();
  });
}

// Hook for fetching popular today's matches
export function usePopularTodayMatches() {
  return useApiRequest(async () => {
    return await streamedApi.getTodayPopularMatches();
  });
}

// Hook for fetching streams for a match (updated for new API)
export function useStreams(source: string | null, id: string | null) {
  return useApiRequest(
    async () => {
      if (!source || !id) return [];
      return await streamedApi.getStreams(source, id);
    },
    [source, id]
  );
}

// Hook for fetching streams from multiple sources for a match
export function useAllStreamsForMatch(match: APIMatch | null) {
  const [allStreams, setAllStreams] = useState<{ [key: string]: Stream[] }>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!match || !match.sources || match.sources.length === 0) {
      setAllStreams({});
      return;
    }

    const fetchAllStreams = async () => {
      setLoading(true);
      setError(null);
      const streamsBySource: { [key: string]: Stream[] } = {};

      try {
        // Fetch streams from all available sources
        const streamPromises = match.sources.map(async (source) => {
          try {
            const streams = await streamedApi.getStreams(source.source, source.id);
            streamsBySource[source.source] = streams;
          } catch (err) {
            console.error(`Failed to fetch streams from ${source.source}:`, err);
            streamsBySource[source.source] = [];
          }
        });

        await Promise.all(streamPromises);
        setAllStreams(streamsBySource);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch streams');
      } finally {
        setLoading(false);
      }
    };

    fetchAllStreams();
  }, [match]);

  // Flatten all streams into a single array
  const flatStreams = Object.values(allStreams).flat();

  return {
    allStreams,
    flatStreams,
    loading,
    error,
    hasStreams: flatStreams.length > 0
  };
}

// Hook for searching matches by team
export function useMatchSearch(teamName: string) {
  return useApiRequest(
    async () => {
      if (!teamName.trim()) return [];
      return await streamedApiUtils.searchMatchesByTeam(teamName);
    },
    [teamName]
  );
}

// Hook for fetching team images
export function useTeamImage(teamName: string | null) {
  return useApiRequest(
    async () => {
      if (!teamName) return null;
      return await streamedApi.getTeamImage(teamName);
    },
    [teamName]
  );
}

// Hook for fetching event images
export function useEventImage(eventId: string | null) {
  return useApiRequest(
    async () => {
      if (!eventId) return null;
      return await streamedApi.getEventImage(eventId);
    },
    [eventId]
  );
}

// Hook for search functionality (updated for APIMatch)
export function useSearch(query: string) {
  const [results, setResults] = useState<APIMatch[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const matches = await streamedApiUtils.searchMatchesByTeam(searchQuery);
      setResults(matches);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      search(query);
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [query, search]);

  return { results, loading, error, search };
}

// Hook for real-time updates (WebSocket)
export function useRealTimeUpdates(gameId: string | null) {
  const [updates, setUpdates] = useState<any[]>([]);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    if (!gameId) return;

    // This would connect to a WebSocket for real-time updates
    // Implementation depends on the actual WebSocket API
    const wsUrl = `${process.env.NEXT_PUBLIC_WS_URL || 'wss://ws.streamed.tv'}/games/${gameId}`;
    
    try {
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        setConnected(true);
      };
      
      ws.onmessage = (event) => {
        const update = JSON.parse(event.data);
        setUpdates(prev => [...prev, update]);
      };
      
      ws.onclose = () => {
        setConnected(false);
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnected(false);
      };

      return () => {
        ws.close();
      };
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
    }
  }, [gameId]);

  return { updates, connected };
}

// Hook for managing user preferences
export function useUserPreferences() {
  const [preferences, setPreferences] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('userPreferences');
      return saved ? JSON.parse(saved) : {
        favoriteTeams: [],
        favoriteSports: [],
        preferredQuality: '720p',
        preferredLanguage: 'English',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    }
    return null;
  });

  const updatePreferences = useCallback((newPreferences: any) => {
    setPreferences(newPreferences);
    if (typeof window !== 'undefined') {
      localStorage.setItem('userPreferences', JSON.stringify(newPreferences));
    }
  }, []);

  return { preferences, updatePreferences };
}
