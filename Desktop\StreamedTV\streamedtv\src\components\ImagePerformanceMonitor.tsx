'use client';

import { useState, useEffect } from 'react';
import { useImagePerformanceMonitor } from '@/hooks/useOptimizedImages';
import { getImageCacheStats } from '@/services/imageOptimization';

interface ImagePerformanceMonitorProps {
  show?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export default function ImagePerformanceMonitor({ 
  show = false, 
  position = 'bottom-right' 
}: ImagePerformanceMonitorProps) {
  const [isVisible, setIsVisible] = useState(show);
  const [detailedView, setDetailedView] = useState(false);
  const stats = useImagePerformanceMonitor();
  const [cacheStats, setCacheStats] = useState(getImageCacheStats());

  useEffect(() => {
    const interval = setInterval(() => {
      setCacheStats(getImageCacheStats());
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development' && !show) {
    return null;
  }

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`fixed ${positionClasses[position]} z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors`}
        title="Show Image Performance Monitor"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </button>
    );
  }

  return (
    <div className={`fixed ${positionClasses[position]} z-50 bg-gray-900 border border-gray-700 rounded-lg shadow-xl text-white text-xs max-w-xs`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="font-semibold">Image Performance</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setDetailedView(!detailedView)}
            className="text-gray-400 hover:text-white transition-colors"
            title="Toggle detailed view"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white transition-colors"
            title="Hide monitor"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Basic Stats */}
      <div className="p-3 space-y-2">
        <div className="flex justify-between">
          <span className="text-gray-400">Cached Images:</span>
          <span className="text-green-400">{cacheStats.totalCached}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Loaded:</span>
          <span className="text-green-400">{cacheStats.loadedCount}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Failed:</span>
          <span className="text-red-400">{cacheStats.errorCount}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-400">Cache Hit Rate:</span>
          <span className="text-blue-400">{stats.cacheHitRate.toFixed(1)}%</span>
        </div>

        {cacheStats.isPreloading && (
          <div className="flex justify-between">
            <span className="text-gray-400">Preloading:</span>
            <span className="text-yellow-400">
              {cacheStats.queueLength} queued
            </span>
          </div>
        )}
      </div>

      {/* Detailed View */}
      {detailedView && (
        <div className="border-t border-gray-700 p-3 space-y-2">
          <div className="text-gray-300 font-medium mb-2">Cache Details</div>
          
          <div className="flex justify-between">
            <span className="text-gray-400">Expired:</span>
            <span className="text-orange-400">{cacheStats.expiredCount}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-400">Queue Length:</span>
            <span className="text-blue-400">{cacheStats.queueLength}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-400">Preloading:</span>
            <span className={cacheStats.isPreloading ? 'text-yellow-400' : 'text-gray-400'}>
              {cacheStats.isPreloading ? 'Active' : 'Idle'}
            </span>
          </div>

          {/* Performance Indicators */}
          <div className="mt-3 pt-2 border-t border-gray-700">
            <div className="text-gray-300 font-medium mb-2">Performance</div>
            
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-400">Success Rate:</span>
                <span className={`${
                  stats.cacheHitRate > 90 ? 'text-green-400' : 
                  stats.cacheHitRate > 70 ? 'text-yellow-400' : 'text-red-400'
                }`}>
                  {((cacheStats.loadedCount / (cacheStats.loadedCount + cacheStats.errorCount)) * 100 || 0).toFixed(1)}%
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-400">Memory Usage:</span>
                <span className="text-blue-400">
                  {(cacheStats.totalCached * 0.1).toFixed(1)}MB
                </span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="mt-3 pt-2 border-t border-gray-700">
            <button
              onClick={() => {
                // Clear cache
                import('@/services/imageOptimization').then(({ clearImageCache }) => {
                  clearImageCache();
                  setCacheStats(getImageCacheStats());
                });
              }}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-1 px-2 rounded text-xs transition-colors"
            >
              Clear Cache
            </button>
          </div>
        </div>
      )}

      {/* Status Indicator */}
      <div className="px-3 py-2 border-t border-gray-700 bg-gray-800 rounded-b-lg">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            cacheStats.isPreloading ? 'bg-yellow-400 animate-pulse' : 
            cacheStats.errorCount > cacheStats.loadedCount ? 'bg-red-400' :
            'bg-green-400'
          }`}></div>
          <span className="text-gray-400 text-xs">
            {cacheStats.isPreloading ? 'Loading images...' : 
             cacheStats.errorCount > cacheStats.loadedCount ? 'Some errors' :
             'All systems good'}
          </span>
        </div>
      </div>
    </div>
  );
}
