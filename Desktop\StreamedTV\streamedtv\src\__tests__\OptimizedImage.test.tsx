/**
 * OptimizedImage Component Tests
 * Tests for image loading, error handling, and optimization features
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import OptimizedImage, { TeamBadge, MatchPoster } from '@/components/OptimizedImage';

// Mock the image optimization service
jest.mock('@/services/imageOptimization', () => ({
  imageOptimizationService: {
    preloadImage: jest.fn(),
    getCachedImage: jest.fn(),
    clearCache: jest.fn(),
    getCacheStats: jest.fn(() => ({
      totalCached: 0,
      loadedCount: 0,
      errorCount: 0,
      expiredCount: 0,
      queueLength: 0,
      isPreloading: false
    }))
  },
  preloadImages: jest.fn(),
  queueImagePreload: jest.fn(),
  getCachedImage: jest.fn(),
  clearImageCache: jest.fn(),
  getImageCacheStats: jest.fn(() => ({
    totalCached: 0,
    loadedCount: 0,
    errorCount: 0,
    expiredCount: 0,
    queueLength: 0,
    isPreloading: false
  }))
}));

// Mock the API utils
jest.mock('@/services/api', () => ({
  streamedApiUtils: {
    getImageUrl: jest.fn((id) => `https://api.streamed.su/api/images/badge/${id}.webp`),
    getMatchPosterUrl: jest.fn((home, away) => `https://api.streamed.su/api/images/poster/${home}/${away}.webp`),
    getProxiedImageUrl: jest.fn((poster) => `https://api.streamed.su/api/images/proxy/${poster}.webp`)
  }
}));

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe('OptimizedImage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with basic props', () => {
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
      />
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toBeInTheDocument();
  });

  it('should show loading placeholder initially', () => {
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
      />
    );

    // Should show loading placeholder
    const placeholder = screen.getByRole('img').parentElement;
    expect(placeholder).toHaveClass('relative');
  });

  it('should handle missing src gracefully', () => {
    render(
      <OptimizedImage
        alt="Test image"
        width={200}
        height={150}
      />
    );

    // Should show placeholder when no src
    const placeholder = screen.getByRole('img', { hidden: true }).parentElement;
    expect(placeholder).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
        className="custom-class"
      />
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('should handle priority loading', () => {
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
        priority={true}
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('loading', 'eager');
  });

  it('should handle lazy loading by default', () => {
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('loading', 'lazy');
  });

  it('should call onLoad callback when image loads', async () => {
    const onLoad = jest.fn();
    
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
        onLoad={onLoad}
      />
    );

    const img = screen.getByRole('img');
    fireEvent.load(img);

    expect(onLoad).toHaveBeenCalled();
  });

  it('should call onError callback when image fails', async () => {
    const onError = jest.fn();
    
    render(
      <OptimizedImage
        src="https://example.com/test.jpg"
        alt="Test image"
        width={200}
        height={150}
        onError={onError}
      />
    );

    const img = screen.getByRole('img');
    fireEvent.error(img);

    expect(onError).toHaveBeenCalled();
  });
});

describe('TeamBadge', () => {
  it('should render team badge with correct props', () => {
    render(
      <TeamBadge
        badgeId="lakers"
        teamName="Lakers"
        size={32}
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('alt', 'Lakers badge');
    expect(img).toHaveAttribute('width', '32');
    expect(img).toHaveAttribute('height', '32');
  });

  it('should show team initial when no badge ID', () => {
    render(
      <TeamBadge
        teamName="Lakers"
        size={32}
      />
    );

    const initial = screen.getByText('L');
    expect(initial).toBeInTheDocument();
  });

  it('should apply size-based optimizations', () => {
    render(
      <TeamBadge
        badgeId="lakers"
        teamName="Lakers"
        size={64}
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('loading', 'eager'); // Large badges should be priority
  });

  it('should handle custom className', () => {
    render(
      <TeamBadge
        badgeId="lakers"
        teamName="Lakers"
        size={32}
        className="custom-badge"
      />
    );

    const container = screen.getByRole('img').parentElement;
    expect(container).toHaveClass('custom-badge');
  });
});

describe('MatchPoster', () => {
  it('should render match poster with team badges', () => {
    render(
      <MatchPoster
        homeBadge="lakers"
        awayBadge="warriors"
        matchTitle="Lakers vs Warriors"
        homeTeam="Lakers"
        awayTeam="Warriors"
        size="medium"
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('alt', 'Lakers vs Warriors poster');
  });

  it('should show custom fallback when no poster available', () => {
    render(
      <MatchPoster
        matchTitle="Test Match"
        homeTeam="Team A"
        awayTeam="Team B"
        size="medium"
        showFallback={true}
      />
    );

    const fallback = screen.getByText('Test Match');
    expect(fallback).toBeInTheDocument();
    
    const vsText = screen.getByText('VS');
    expect(vsText).toBeInTheDocument();
  });

  it('should return null when showFallback is false and no poster', () => {
    const { container } = render(
      <MatchPoster
        matchTitle="Test Match"
        homeTeam="Team A"
        awayTeam="Team B"
        size="medium"
        showFallback={false}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should apply size presets correctly', () => {
    render(
      <MatchPoster
        homeBadge="lakers"
        awayBadge="warriors"
        matchTitle="Lakers vs Warriors"
        size="hero"
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('width', '600');
    expect(img).toHaveAttribute('height', '360');
    expect(img).toHaveAttribute('loading', 'eager'); // Hero should be priority
  });

  it('should handle different size variations', () => {
    const sizes = ['small', 'medium', 'large', 'hero'] as const;
    const expectedDimensions = {
      small: { width: 200, height: 120 },
      medium: { width: 300, height: 180 },
      large: { width: 400, height: 240 },
      hero: { width: 600, height: 360 }
    };

    sizes.forEach(size => {
      const { unmount } = render(
        <MatchPoster
          homeBadge="lakers"
          awayBadge="warriors"
          matchTitle="Test Match"
          size={size}
        />
      );

      const img = screen.getByRole('img');
      expect(img).toHaveAttribute('width', expectedDimensions[size].width.toString());
      expect(img).toHaveAttribute('height', expectedDimensions[size].height.toString());
      
      unmount();
    });
  });

  it('should prioritize poster over generated poster', () => {
    render(
      <MatchPoster
        homeBadge="lakers"
        awayBadge="warriors"
        poster="custom-poster"
        matchTitle="Lakers vs Warriors"
        size="medium"
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('src', 'https://api.streamed.su/api/images/proxy/custom-poster.webp');
  });

  it('should use generated poster when no custom poster', () => {
    render(
      <MatchPoster
        homeBadge="lakers"
        awayBadge="warriors"
        matchTitle="Lakers vs Warriors"
        size="medium"
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('src', 'https://api.streamed.su/api/images/poster/lakers/warriors.webp');
  });

  it('should apply custom width and height over size presets', () => {
    render(
      <MatchPoster
        homeBadge="lakers"
        awayBadge="warriors"
        matchTitle="Lakers vs Warriors"
        size="medium"
        width={500}
        height={300}
      />
    );

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('width', '500');
    expect(img).toHaveAttribute('height', '300');
  });
});
