// Streamed API Response Types (based on actual API structure)
export interface StreamedApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// Sports Categories from /api/sports (actual API structure)
export interface Sport {
  id: string;    // Sport identifier (used in Matches API endpoints)
  name: string;  // Display name of the sport
}

// Match data from /api/matches/* (actual API structure)
export interface APIMatch {
  id: string;
  title: string;
  category: string;
  date: number; // Unix timestamp in milliseconds
  poster?: string; // URL path to match poster image
  popular: boolean;
  teams?: {
    home?: {
      name: string;
      badge: string; // URL path to home team badge
    };
    away?: {
      name: string;
      badge: string; // URL path to away team badge
    };
  };
  sources: {
    source: string; // Stream source identifier (e.g. "alpha", "bravo")
    id: string; // Source-specific match ID
  }[];
}

// Legacy interface for backward compatibility (will be phased out)
export interface StreamedMatch {
  id: string;
  title: string;
  date: string;
  time: string;
  status: string;
  homeTeam: {
    name: string;
    logo?: string;
  };
  awayTeam: {
    name: string;
    logo?: string;
  };
  sport: string;
  league?: string;
  sources: StreamSource[];
}

export interface StreamSource {
  source: string;
  id: string;
  name: string;
  quality?: string;
  language?: string;
}

// Stream data from /api/stream/{source}/{id} (actual API structure)
export interface Stream {
  id: string;        // Unique identifier for the stream
  streamNo: number;  // Stream number/index
  language: string;  // Stream language (e.g., "English", "Spanish")
  hd: boolean;       // Whether the stream is in HD quality
  embedUrl: string;  // URL that can be used to embed the stream
  source: string;    // Source identifier (e.g., "alpha", "bravo")
}

// API response for stream endpoints (returns array of streams)
export interface StreamResponse extends Array<Stream> {}

// Legacy interfaces for backward compatibility
export interface StreamData {
  streams: StreamLink[];
  match?: StreamedMatch;
  error?: string;
}

export interface StreamLink {
  url: string;
  quality: string;
  type: string;
  language?: string;
  name?: string;
}

// Image data from /api/images
export interface TeamImage {
  teamName: string;
  logoUrl: string;
}

export interface EventImage {
  eventId: string;
  posterUrl: string;
}

// User Preferences (local storage)
export interface UserPreferences {
  favoriteTeams: string[];
  favoriteSports: string[];
  preferredQuality: string;
  preferredLanguage: string;
  timezone: string;
}

// Processed data types for UI components
export interface ProcessedMatch {
  id: string;
  title: string;
  homeTeam: string;
  awayTeam: string;
  sport: string;
  date: string;
  time: string;
  status: 'live' | 'upcoming' | 'finished';
  streamCount: number;
  sources: StreamSource[];
}

export interface ProcessedSport {
  name: string;
  slug: string;
  displayName: string;
  matchCount: number;
  isActive: boolean;
}

// API Configuration for Streamed API
export interface StreamedApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
}

// Error Types
export interface StreamedApiError {
  code: string;
  message: string;
  details?: any;
}

// API Client Interface (updated for new stream endpoints)
export interface StreamedApiClient {
  // Sports endpoints
  getSports(): Promise<Sport[]>;

  // Matches endpoints (new structure)
  getMatches(sport: string): Promise<APIMatch[]>;
  getPopularMatches(sport: string): Promise<APIMatch[]>;
  getAllMatches(): Promise<APIMatch[]>;
  getAllPopularMatches(): Promise<APIMatch[]>;
  getTodayMatches(): Promise<APIMatch[]>;
  getTodayPopularMatches(): Promise<APIMatch[]>;
  getLiveMatches(): Promise<APIMatch[]>;
  getLivePopularMatches(): Promise<APIMatch[]>;

  // Streams endpoints (updated for new API)
  getStreams(source: string, id: string): Promise<Stream[]>;

  // Images endpoints (updated for actual API structure)
  getTeamBadgeUrl(badgeId: string): string;
  getMatchPosterUrl(homeBadge: string, awayBadge: string): string;
  getProxiedImageUrl(poster: string): string;

  // Legacy methods for backward compatibility
  getTeamImage(badgeId: string): Promise<string | null>;
  getEventImage(poster: string): Promise<string | null>;
}
