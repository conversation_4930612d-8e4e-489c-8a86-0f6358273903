import Link from 'next/link';

const Footer = () => {
  return (
    <footer className="bg-gray-950 border-t border-gray-800 mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <div className="text-2xl font-bold text-white">
                Streamed
              </div>
            </Link>
            <p className="text-gray-400 text-sm mb-4">
              Watch any live sport online. Best source to watch NBA, NHL, MLB, UFC for free!
            </p>
            <div className="flex space-x-4">
              <Link 
                href="https://discord.gg/KHC4xTYZgv" 
                className="text-gray-400 hover:text-white transition-colors duration-200"
                target="_blank"
                rel="noopener noreferrer"
              >
                Discord
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/schedule" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Schedule
                </Link>
              </li>
              <li>
                <Link href="/docs" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  API
                </Link>
              </li>
              <li>
                <Link href="/status" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Status
                </Link>
              </li>
            </ul>
          </div>

          {/* Sports Categories */}
          <div>
            <h3 className="text-white font-semibold mb-4">Sports</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/football" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Football
                </Link>
              </li>
              <li>
                <Link href="/basketball" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Basketball
                </Link>
              </li>
              <li>
                <Link href="/hockey" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Hockey
                </Link>
              </li>
              <li>
                <Link href="/baseball" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                  Baseball
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 Streamed. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
