import {
  StreamedApiConfig,
  StreamedApiError,
  Sport,
  APIMatch,
  Stream,
  StreamedMatch,
  StreamData,
  StreamedApiClient
} from '@/types/api';

class StreamedApiClientImpl implements StreamedApiClient {
  private config: StreamedApiConfig;

  constructor(config: StreamedApiConfig) {
    this.config = config;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;

    const defaultHeaders: HeadersInit = {
      'Accept': 'application/json',
    };

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Streamed API Request failed:', error);
      throw this.handleError(error);
    }
  }

  private handleError(error: any): StreamedApiError {
    if (error.name === 'AbortError') {
      return {
        code: 'TIMEOUT',
        message: 'Request timeout',
        details: error,
      };
    }

    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed',
        details: error,
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      details: error,
    };
  }

  // Sports endpoints - /api/sports
  async getSports(): Promise<Sport[]> {
    try {
      const data = await this.request<Sport[]>('/api/sports');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch sports:', error);
      return [];
    }
  }

  // Matches endpoints (updated for new API structure)

  // Sport-specific matches
  async getMatches(sport: string): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>(`/api/matches/${sport}`);
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error(`Failed to fetch matches for ${sport}:`, error);
      return [];
    }
  }

  async getPopularMatches(sport: string): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>(`/api/matches/${sport}/popular`);
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error(`Failed to fetch popular matches for ${sport}:`, error);
      return [];
    }
  }

  // All matches
  async getAllMatches(): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>('/api/matches/all');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch all matches:', error);
      return [];
    }
  }

  async getAllPopularMatches(): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>('/api/matches/all/popular');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch all popular matches:', error);
      return [];
    }
  }

  // Today's matches
  async getTodayMatches(): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>('/api/matches/all-today');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch today\'s matches:', error);
      return [];
    }
  }

  async getTodayPopularMatches(): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>('/api/matches/all-today/popular');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch today\'s popular matches:', error);
      return [];
    }
  }

  // Live matches
  async getLiveMatches(): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>('/api/matches/live');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch live matches:', error);
      return [];
    }
  }

  async getLivePopularMatches(): Promise<APIMatch[]> {
    try {
      const data = await this.request<APIMatch[]>('/api/matches/live/popular');
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Failed to fetch live popular matches:', error);
      return [];
    }
  }

  // Streams endpoints - /api/stream/{source}/{id} (updated for new API)
  async getStreams(source: string, id: string): Promise<Stream[]> {
    try {
      // Validate source (must be one of the supported sources)
      const validSources = ['alpha', 'bravo', 'charlie', 'delta', 'echo', 'foxtrot', 'golf', 'hotel', 'intel'];
      if (!validSources.includes(source.toLowerCase())) {
        console.warn(`Invalid source: ${source}. Valid sources are: ${validSources.join(', ')}`);
        return [];
      }

      const data = await this.request<Stream[]>(`/api/stream/${source}/${id}`);
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error(`Failed to fetch streams for ${source}/${id}:`, error);
      return [];
    }
  }

  // Images endpoints - /api/images (updated for actual API structure)

  // Note: Image endpoints return actual image files, not JSON
  // These methods return the constructed URLs for direct use in img tags

  getTeamBadgeUrl(badgeId: string): string {
    // Ensure badgeId is properly formatted (remove any existing .webp extension)
    const cleanBadgeId = badgeId.replace(/\.webp$/, '');
    // URL encode the badge ID to handle special characters
    const encodedBadgeId = encodeURIComponent(cleanBadgeId);
    return `${this.config.baseUrl}/api/images/badge/${encodedBadgeId}.webp`;
  }

  getMatchPosterUrl(homeBadge: string, awayBadge: string): string {
    return `${this.config.baseUrl}/api/images/poster/${homeBadge}/${awayBadge}.webp`;
  }

  getProxiedImageUrl(poster: string): string {
    return `${this.config.baseUrl}/api/images/proxy/${poster}.webp`;
  }

  // Legacy methods for backward compatibility
  async getTeamImage(badgeId: string): Promise<string | null> {
    try {
      return this.getTeamBadgeUrl(badgeId);
    } catch (error) {
      console.error(`Failed to construct team badge URL for ${badgeId}:`, error);
      return null;
    }
  }

  async getEventImage(poster: string): Promise<string | null> {
    try {
      return this.getProxiedImageUrl(poster);
    } catch (error) {
      console.error(`Failed to construct proxied image URL for ${poster}:`, error);
      return null;
    }
  }
}

// Default configuration for Streamed API
const defaultConfig: StreamedApiConfig = {
  baseUrl: 'https://streamed.pk',
  timeout: 10000,
  retries: 3,
};

// Create and export API client instance
export const streamedApi = new StreamedApiClientImpl(defaultConfig);

// Export the class for custom configurations
export { StreamedApiClientImpl };

// Utility functions for common operations (updated for new API)
export const streamedApiUtils = {
  // Get live matches (now uses dedicated endpoint)
  getLiveMatches: async (): Promise<APIMatch[]> => {
    try {
      return await streamedApi.getLiveMatches();
    } catch (error) {
      console.error('Failed to fetch live matches:', error);
      return [];
    }
  },

  // Get popular live matches
  getPopularLiveMatches: async (): Promise<APIMatch[]> => {
    try {
      return await streamedApi.getLivePopularMatches();
    } catch (error) {
      console.error('Failed to fetch popular live matches:', error);
      return [];
    }
  },

  // Get today's matches (now uses dedicated endpoint)
  getTodayMatches: async (): Promise<APIMatch[]> => {
    try {
      return await streamedApi.getTodayMatches();
    } catch (error) {
      console.error('Failed to fetch today\'s matches:', error);
      return [];
    }
  },

  // Get popular matches for today
  getPopularTodayMatches: async (): Promise<APIMatch[]> => {
    try {
      return await streamedApi.getTodayPopularMatches();
    } catch (error) {
      console.error('Failed to fetch popular today\'s matches:', error);
      return [];
    }
  },

  // Get matches by sport
  getMatchesBySport: async (sport: string): Promise<APIMatch[]> => {
    try {
      return await streamedApi.getMatches(sport);
    } catch (error) {
      console.error(`Failed to fetch matches for ${sport}:`, error);
      return [];
    }
  },

  // Get popular matches by sport
  getPopularMatchesBySport: async (sport: string): Promise<APIMatch[]> => {
    try {
      return await streamedApi.getPopularMatches(sport);
    } catch (error) {
      console.error(`Failed to fetch popular matches for ${sport}:`, error);
      return [];
    }
  },

  // Search matches by team name
  searchMatchesByTeam: async (teamName: string): Promise<APIMatch[]> => {
    try {
      const allMatches = await streamedApi.getAllMatches();
      const searchTerm = teamName.toLowerCase();

      return allMatches.filter(match => {
        if (!match.teams) return false;

        const homeTeamMatch = match.teams.home?.name.toLowerCase().includes(searchTerm);
        const awayTeamMatch = match.teams.away?.name.toLowerCase().includes(searchTerm);

        return homeTeamMatch || awayTeamMatch;
      });
    } catch (error) {
      console.error('Failed to search matches by team:', error);
      return [];
    }
  },

  // Helper function to format match date
  formatMatchDate: (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString();
  },

  // Helper function to format match time
  formatMatchTime: (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  },

  // Helper function to check if match is live (based on timestamp)
  isMatchLive: (timestamp: number): boolean => {
    const now = Date.now();
    const matchTime = timestamp;
    const timeDiff = now - matchTime;

    // Consider a match live if it started within the last 3 hours
    return timeDiff >= 0 && timeDiff <= 3 * 60 * 60 * 1000;
  },

  // Helper functions for image URLs (updated for actual API endpoints)
  getTeamBadgeUrl: (badgeId: string): string => {
    return streamedApi.getTeamBadgeUrl(badgeId);
  },

  getMatchPosterUrl: (homeBadge: string, awayBadge: string): string => {
    return streamedApi.getMatchPosterUrl(homeBadge, awayBadge);
  },

  getProxiedImageUrl: (poster: string): string => {
    return streamedApi.getProxiedImageUrl(poster);
  },

  // Legacy function for backward compatibility
  getImageUrl: (imagePath: string): string => {
    // If it's already a full URL, return as-is
    if (imagePath.startsWith('http')) return imagePath;

    // If it starts with /api/images, it's already a proper path
    if (imagePath.startsWith('/api/images')) {
      return `https://streamed.pk${imagePath}`;
    }

    // Otherwise, assume it's a badge ID and construct the URL
    return streamedApi.getTeamBadgeUrl(imagePath);
  },
};
