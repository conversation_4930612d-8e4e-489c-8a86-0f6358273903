import { useEffect, useState, useCallback } from 'react';
import { imageOptimizationService, queueImagePreload } from '@/services/imageOptimization';
import { APIMatch } from '@/types/api';
import { streamedApiUtils } from '@/services/api';

/**
 * Hook for optimized image loading with preloading and caching
 */
export function useOptimizedImage(src?: string) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(!!src);

  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setHasError(false);
    setIsLoaded(false);

    // Check cache first
    const cached = imageOptimizationService.getCachedImage(src);
    if (cached?.loaded) {
      setIsLoaded(true);
      setIsLoading(false);
      return;
    }

    if (cached?.error) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    // Preload the image
    imageOptimizationService.preloadImage(src)
      .then(() => {
        setIsLoaded(true);
        setIsLoading(false);
      })
      .catch(() => {
        setHasError(true);
        setIsLoading(false);
      });
  }, [src]);

  return { isLoaded, hasError, isLoading };
}

/**
 * Hook for preloading match images (badges and posters)
 */
export function useMatchImagePreloader(matches: APIMatch[]) {
  const [preloadedCount, setPreloadedCount] = useState(0);
  const [totalImages, setTotalImages] = useState(0);

  const preloadMatchImages = useCallback((matchList: APIMatch[]) => {
    const imageUrls: string[] = [];

    matchList.forEach(match => {
      // Add team badges
      if (match.teams?.home?.badge) {
        imageUrls.push(streamedApiUtils.getImageUrl(match.teams.home.badge));
      }
      if (match.teams?.away?.badge) {
        imageUrls.push(streamedApiUtils.getImageUrl(match.teams.away.badge));
      }

      // Add match poster
      if (match.poster) {
        imageUrls.push(streamedApiUtils.getProxiedImageUrl(match.poster));
      } else if (match.teams?.home?.badge && match.teams?.away?.badge) {
        imageUrls.push(streamedApiUtils.getMatchPosterUrl(
          match.teams.home.badge,
          match.teams.away.badge
        ));
      }
    });

    setTotalImages(imageUrls.length);
    
    if (imageUrls.length > 0) {
      // Queue images for preloading
      queueImagePreload(imageUrls);
      
      // Track progress (simplified - in real implementation you'd track individual loads)
      let loaded = 0;
      imageUrls.forEach(url => {
        imageOptimizationService.preloadImage(url)
          .then(() => {
            loaded++;
            setPreloadedCount(loaded);
          })
          .catch(() => {
            loaded++;
            setPreloadedCount(loaded);
          });
      });
    }
  }, []);

  useEffect(() => {
    if (matches.length > 0) {
      preloadMatchImages(matches);
    }
  }, [matches, preloadMatchImages]);

  return {
    preloadedCount,
    totalImages,
    progress: totalImages > 0 ? (preloadedCount / totalImages) * 100 : 0,
    isComplete: preloadedCount === totalImages && totalImages > 0
  };
}

/**
 * Hook for intelligent image preloading based on user behavior
 */
export function useIntelligentPreloader() {
  const [isPreloading, setIsPreloading] = useState(false);

  const preloadImagesForPage = useCallback(async (pageType: 'home' | 'sport' | 'match', data?: any) => {
    setIsPreloading(true);

    try {
      switch (pageType) {
        case 'home':
          // Preload common team badges and popular match posters
          if (data?.matches) {
            const priorityImages = data.matches
              .slice(0, 10) // First 10 matches
              .flatMap((match: APIMatch) => [
                match.teams?.home?.badge && streamedApiUtils.getImageUrl(match.teams.home.badge),
                match.teams?.away?.badge && streamedApiUtils.getImageUrl(match.teams.away.badge)
              ])
              .filter(Boolean);
            
            await imageOptimizationService.preloadImages(priorityImages, 5);
          }
          break;

        case 'sport':
          // Preload all visible match images
          if (data?.matches) {
            const sportImages = data.matches
              .slice(0, 20) // First 20 matches
              .flatMap((match: APIMatch) => [
                match.teams?.home?.badge && streamedApiUtils.getImageUrl(match.teams.home.badge),
                match.teams?.away?.badge && streamedApiUtils.getImageUrl(match.teams.away.badge),
                match.poster && streamedApiUtils.getProxiedImageUrl(match.poster)
              ])
              .filter(Boolean);
            
            await imageOptimizationService.preloadImages(sportImages, 3);
          }
          break;

        case 'match':
          // Preload high-quality match poster and team badges
          if (data?.match) {
            const matchImages = [
              data.match.teams?.home?.badge && streamedApiUtils.getImageUrl(data.match.teams.home.badge),
              data.match.teams?.away?.badge && streamedApiUtils.getImageUrl(data.match.teams.away.badge),
              data.match.poster && streamedApiUtils.getProxiedImageUrl(data.match.poster)
            ].filter(Boolean);
            
            await imageOptimizationService.preloadImages(matchImages, 2);
          }
          break;
      }
    } catch (error) {
      console.warn('Error preloading images:', error);
    } finally {
      setIsPreloading(false);
    }
  }, []);

  return { preloadImagesForPage, isPreloading };
}

/**
 * Hook for responsive image loading
 */
export function useResponsiveImage(
  baseSrc: string,
  breakpoints: { width: number; src: string }[]
) {
  const [currentSrc, setCurrentSrc] = useState(baseSrc);
  const [currentBreakpoint, setCurrentBreakpoint] = useState<number>(0);

  useEffect(() => {
    const updateImage = () => {
      const windowWidth = window.innerWidth;
      const devicePixelRatio = window.devicePixelRatio || 1;
      const effectiveWidth = windowWidth * devicePixelRatio;

      // Find the best image for current screen size
      const sortedBreakpoints = [...breakpoints].sort((a, b) => a.width - b.width);
      
      let selectedImage = { src: baseSrc, width: 0 };
      for (const breakpoint of sortedBreakpoints) {
        if (effectiveWidth >= breakpoint.width) {
          selectedImage = breakpoint;
        } else {
          break;
        }
      }

      if (selectedImage.src !== currentSrc) {
        setCurrentSrc(selectedImage.src);
        setCurrentBreakpoint(selectedImage.width);
      }
    };

    updateImage();
    
    const debouncedUpdate = debounce(updateImage, 250);
    window.addEventListener('resize', debouncedUpdate);
    
    return () => window.removeEventListener('resize', debouncedUpdate);
  }, [baseSrc, breakpoints, currentSrc]);

  return { currentSrc, currentBreakpoint };
}

/**
 * Hook for image loading performance monitoring
 */
export function useImagePerformanceMonitor() {
  const [stats, setStats] = useState({
    totalLoaded: 0,
    totalFailed: 0,
    averageLoadTime: 0,
    cacheHitRate: 0
  });

  const updateStats = useCallback(() => {
    const cacheStats = imageOptimizationService.getCacheStats();
    
    setStats({
      totalLoaded: cacheStats.loadedCount,
      totalFailed: cacheStats.errorCount,
      averageLoadTime: 0, // Would need to track load times
      cacheHitRate: cacheStats.totalCached > 0 
        ? (cacheStats.loadedCount / cacheStats.totalCached) * 100 
        : 0
    });
  }, []);

  useEffect(() => {
    updateStats();
    
    // Update stats periodically
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, [updateStats]);

  return stats;
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
