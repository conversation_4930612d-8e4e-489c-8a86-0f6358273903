'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { TeamBadge, MatchPoster, OptimizedImage, ResponsiveImage, ProgressiveImage } from '@/components/OptimizedImage';
import { useMatchImagePreloader, useIntelligentPreloader, useImagePerformanceMonitor } from '@/hooks/useOptimizedImages';
import { getImageCacheStats, clearImageCache, preloadImages } from '@/services/imageOptimization';
import { streamedApiUtils } from '@/services/api';

// Test data for image testing
const testMatches = [
  {
    id: 'test-1',
    title: 'Lakers vs Warriors',
    teams: {
      home: { name: '<PERSON>', badge: 'lakers' },
      away: { name: 'Warriors', badge: 'warriors' }
    },
    poster: 'lakers-warriors-poster'
  },
  {
    id: 'test-2', 
    title: 'Real Madrid vs Barcelona',
    teams: {
      home: { name: 'Real Madrid', badge: 'real-madrid' },
      away: { name: 'Barcelona', badge: 'barcelona' }
    },
    poster: 'clasico-poster'
  },
  {
    id: 'test-3',
    title: 'Manchester United vs Liverpool',
    teams: {
      home: { name: 'Manchester United', badge: 'man-united' },
      away: { name: 'Liverpool', badge: 'liverpool' }
    }
  }
];

const testBadges = [
  { id: 'lakers', name: 'Lakers' },
  { id: 'warriors', name: 'Warriors' },
  { id: 'real-madrid', name: 'Real Madrid' },
  { id: 'barcelona', name: 'Barcelona' },
  { id: 'man-united', name: 'Manchester United' },
  { id: 'liverpool', name: 'Liverpool' },
  { id: 'invalid-badge', name: 'Invalid Badge' }
];

export default function TestImagesPage() {
  const [cacheStats, setCacheStats] = useState(getImageCacheStats());
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [isRunningTests, setIsRunningTests] = useState(false);
  
  const { preloadImagesForPage } = useIntelligentPreloader();
  const imagePreloader = useMatchImagePreloader(testMatches as any);
  const performanceStats = useImagePerformanceMonitor();

  // Update cache stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setCacheStats(getImageCacheStats());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const runImageTests = async () => {
    setIsRunningTests(true);
    const results: Record<string, boolean> = {};

    try {
      // Test 1: Basic image loading
      console.log('🧪 Testing basic image loading...');
      const basicImageUrl = streamedApiUtils.getImageUrl('lakers');
      const img = new Image();
      const basicLoadPromise = new Promise<boolean>((resolve) => {
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = basicImageUrl;
      });
      results.basicImageLoading = await basicLoadPromise;

      // Test 2: Preloading functionality
      console.log('🧪 Testing preloading functionality...');
      const preloadUrls = testBadges.slice(0, 3).map(badge => streamedApiUtils.getImageUrl(badge.id));
      try {
        await preloadImages(preloadUrls);
        results.preloadingFunctionality = true;
      } catch {
        results.preloadingFunctionality = false;
      }

      // Test 3: Cache functionality
      console.log('🧪 Testing cache functionality...');
      const cacheStatsBefore = getImageCacheStats();
      await preloadImages([streamedApiUtils.getImageUrl('warriors')]);
      const cacheStatsAfter = getImageCacheStats();
      results.cacheFunctionality = cacheStatsAfter.totalCached > cacheStatsBefore.totalCached;

      // Test 4: Error handling
      console.log('🧪 Testing error handling...');
      const invalidImageUrl = streamedApiUtils.getImageUrl('definitely-invalid-badge-id-12345');
      const errorImg = new Image();
      const errorHandlingPromise = new Promise<boolean>((resolve) => {
        errorImg.onload = () => resolve(false); // Should not load
        errorImg.onerror = () => resolve(true); // Should error
        errorImg.src = invalidImageUrl;
      });
      results.errorHandling = await errorHandlingPromise;

      // Test 5: Match poster generation
      console.log('🧪 Testing match poster generation...');
      const posterUrl = streamedApiUtils.getMatchPosterUrl('lakers', 'warriors');
      const posterImg = new Image();
      const posterPromise = new Promise<boolean>((resolve) => {
        posterImg.onload = () => resolve(true);
        posterImg.onerror = () => resolve(false);
        posterImg.src = posterUrl;
      });
      results.matchPosterGeneration = await posterPromise;

      // Test 6: Proxied image loading
      console.log('🧪 Testing proxied image loading...');
      const proxiedUrl = streamedApiUtils.getProxiedImageUrl('test-poster');
      const proxiedImg = new Image();
      const proxiedPromise = new Promise<boolean>((resolve) => {
        const timeout = setTimeout(() => resolve(false), 5000); // 5 second timeout
        proxiedImg.onload = () => {
          clearTimeout(timeout);
          resolve(true);
        };
        proxiedImg.onerror = () => {
          clearTimeout(timeout);
          resolve(false);
        };
        proxiedImg.src = proxiedUrl;
      });
      results.proxiedImageLoading = await proxiedPromise;

      setTestResults(results);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  const clearCache = () => {
    clearImageCache();
    setCacheStats(getImageCacheStats());
  };

  const testPassCount = Object.values(testResults).filter(Boolean).length;
  const testTotalCount = Object.keys(testResults).length;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <nav className="mb-4">
          <Link href="/" className="text-blue-400 hover:text-blue-300">
            ← Back to Home
          </Link>
        </nav>
        <h1 className="text-4xl font-bold text-white mb-4">Image Integration Test Suite</h1>
        <p className="text-gray-400">
          Comprehensive testing of image loading, caching, and optimization features
        </p>
      </div>

      {/* Test Controls */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Test Controls</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={runImageTests}
            disabled={isRunningTests}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
          </button>
          <button
            onClick={clearCache}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Clear Cache
          </button>
          <button
            onClick={() => preloadImagesForPage('home', { matches: testMatches })}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Test Preloading
          </button>
        </div>
      </div>

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">
            Test Results ({testPassCount}/{testTotalCount} passed)
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(testResults).map(([testName, passed]) => (
              <div
                key={testName}
                className={`p-4 rounded-lg border ${
                  passed 
                    ? 'bg-green-900 border-green-700 text-green-100' 
                    : 'bg-red-900 border-red-700 text-red-100'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <span className={`text-lg ${passed ? 'text-green-400' : 'text-red-400'}`}>
                    {passed ? '✅' : '❌'}
                  </span>
                  <span className="font-medium">
                    {testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Stats */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Performance Statistics</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{cacheStats.totalCached}</div>
            <div className="text-gray-400 text-sm">Cached Images</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{cacheStats.loadedCount}</div>
            <div className="text-gray-400 text-sm">Successfully Loaded</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-400">{cacheStats.errorCount}</div>
            <div className="text-gray-400 text-sm">Failed Loads</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              {performanceStats.cacheHitRate.toFixed(1)}%
            </div>
            <div className="text-gray-400 text-sm">Cache Hit Rate</div>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-700">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Queue Length:</span>
            <span className="text-white">{cacheStats.queueLength}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Preloading:</span>
            <span className={cacheStats.isPreloading ? 'text-yellow-400' : 'text-gray-400'}>
              {cacheStats.isPreloading ? 'Active' : 'Idle'}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Preload Progress:</span>
            <span className="text-blue-400">
              {imagePreloader.preloadedCount}/{imagePreloader.totalImages} 
              ({imagePreloader.progress.toFixed(1)}%)
            </span>
          </div>
        </div>
      </div>

      {/* Team Badge Tests */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Team Badge Tests</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {testBadges.map((badge) => (
            <div key={badge.id} className="text-center">
              <TeamBadge
                badgeId={badge.id}
                teamName={badge.name}
                size={64}
                className="mx-auto mb-2"
              />
              <div className="text-sm text-gray-400">{badge.name}</div>
              <div className="text-xs text-gray-500">{badge.id}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Match Poster Tests */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Match Poster Tests</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testMatches.map((match) => (
            <div key={match.id} className="text-center">
              <MatchPoster
                homeBadge={match.teams?.home?.badge}
                awayBadge={match.teams?.away?.badge}
                poster={match.poster}
                matchTitle={match.title}
                homeTeam={match.teams?.home?.name}
                awayTeam={match.teams?.away?.name}
                size="medium"
                className="mx-auto mb-2"
              />
              <div className="text-sm text-white font-medium">{match.title}</div>
              <div className="text-xs text-gray-400">
                {match.teams?.home?.name} vs {match.teams?.away?.name}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Size Variations Test */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Size Variations Test</h2>
        <div className="space-y-6">
          {['small', 'medium', 'large', 'hero'].map((size) => (
            <div key={size} className="text-center">
              <h3 className="text-lg text-white mb-4 capitalize">{size} Size</h3>
              <MatchPoster
                homeBadge="lakers"
                awayBadge="warriors"
                matchTitle="Lakers vs Warriors"
                homeTeam="Lakers"
                awayTeam="Warriors"
                size={size as any}
                className="mx-auto"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Error Handling Test */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-white mb-4">Error Handling Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Invalid Badge</h3>
            <TeamBadge
              badgeId="definitely-invalid-badge-id"
              teamName="Invalid Team"
              size={64}
              className="mx-auto"
            />
          </div>
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Invalid Poster</h3>
            <MatchPoster
              poster="definitely-invalid-poster-id"
              matchTitle="Invalid Match"
              homeTeam="Team A"
              awayTeam="Team B"
              size="medium"
              className="mx-auto"
            />
          </div>
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">No Data</h3>
            <MatchPoster
              matchTitle="No Data Match"
              homeTeam="Unknown"
              awayTeam="Unknown"
              size="medium"
              className="mx-auto"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
