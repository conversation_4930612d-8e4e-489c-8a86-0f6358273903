import { Stream } from '@/types/api';

export interface StreamPreferences {
  preferHD: boolean;
  preferredLanguage: string;
  preferredSources: string[];
}

export const defaultStreamPreferences: StreamPreferences = {
  preferHD: true,
  preferredLanguage: 'English',
  preferredSources: ['alpha', 'bravo', 'charlie'], // Preferred order
};

/**
 * Sort streams by quality, language preference, and source preference
 */
export function sortStreamsByPreference(
  streams: Stream[], 
  preferences: StreamPreferences = defaultStreamPreferences
): Stream[] {
  return [...streams].sort((a, b) => {
    // 1. Prefer HD streams if preferHD is true
    if (preferences.preferHD) {
      if (a.hd && !b.hd) return -1;
      if (!a.hd && b.hd) return 1;
    } else {
      // Prefer SD streams if preferHD is false
      if (!a.hd && b.hd) return -1;
      if (a.hd && !b.hd) return 1;
    }

    // 2. Prefer streams in the preferred language
    const aLangMatch = a.language.toLowerCase() === preferences.preferredLanguage.toLowerCase();
    const bLangMatch = b.language.toLowerCase() === preferences.preferredLanguage.toLowerCase();
    
    if (aLangMatch && !bLangMatch) return -1;
    if (!aLangMatch && bLangMatch) return 1;

    // 3. Prefer streams from preferred sources
    const aSourceIndex = preferences.preferredSources.indexOf(a.source.toLowerCase());
    const bSourceIndex = preferences.preferredSources.indexOf(b.source.toLowerCase());
    
    // If both sources are in preferences, sort by preference order
    if (aSourceIndex !== -1 && bSourceIndex !== -1) {
      return aSourceIndex - bSourceIndex;
    }
    
    // If only one source is in preferences, prefer it
    if (aSourceIndex !== -1 && bSourceIndex === -1) return -1;
    if (aSourceIndex === -1 && bSourceIndex !== -1) return 1;

    // 4. Finally, sort by stream number
    return a.streamNo - b.streamNo;
  });
}

/**
 * Get the best stream based on preferences
 */
export function getBestStream(
  streams: Stream[], 
  preferences: StreamPreferences = defaultStreamPreferences
): Stream | null {
  if (streams.length === 0) return null;
  
  const sortedStreams = sortStreamsByPreference(streams, preferences);
  return sortedStreams[0];
}

/**
 * Group streams by source
 */
export function groupStreamsBySource(streams: Stream[]): { [source: string]: Stream[] } {
  return streams.reduce((groups, stream) => {
    const source = stream.source;
    if (!groups[source]) {
      groups[source] = [];
    }
    groups[source].push(stream);
    return groups;
  }, {} as { [source: string]: Stream[] });
}

/**
 * Group streams by language
 */
export function groupStreamsByLanguage(streams: Stream[]): { [language: string]: Stream[] } {
  return streams.reduce((groups, stream) => {
    const language = stream.language;
    if (!groups[language]) {
      groups[language] = [];
    }
    groups[language].push(stream);
    return groups;
  }, {} as { [language: string]: Stream[] });
}

/**
 * Get available languages from streams
 */
export function getAvailableLanguages(streams: Stream[]): string[] {
  const languages = new Set(streams.map(stream => stream.language));
  return Array.from(languages).sort();
}

/**
 * Get available sources from streams
 */
export function getAvailableSources(streams: Stream[]): string[] {
  const sources = new Set(streams.map(stream => stream.source));
  return Array.from(sources).sort();
}

/**
 * Filter streams by quality
 */
export function filterStreamsByQuality(streams: Stream[], hdOnly: boolean): Stream[] {
  return streams.filter(stream => hdOnly ? stream.hd : !stream.hd);
}

/**
 * Filter streams by language
 */
export function filterStreamsByLanguage(streams: Stream[], language: string): Stream[] {
  return streams.filter(stream => 
    stream.language.toLowerCase() === language.toLowerCase()
  );
}

/**
 * Filter streams by source
 */
export function filterStreamsBySource(streams: Stream[], source: string): Stream[] {
  return streams.filter(stream => 
    stream.source.toLowerCase() === source.toLowerCase()
  );
}

/**
 * Get stream quality statistics
 */
export function getStreamStats(streams: Stream[]) {
  const total = streams.length;
  const hdCount = streams.filter(s => s.hd).length;
  const sdCount = total - hdCount;
  const languages = getAvailableLanguages(streams);
  const sources = getAvailableSources(streams);

  return {
    total,
    hdCount,
    sdCount,
    languages: languages.length,
    sources: sources.length,
    languageList: languages,
    sourceList: sources,
  };
}

/**
 * Validate stream URL
 */
export function isValidStreamUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get user preferences from localStorage
 */
export function getUserStreamPreferences(): StreamPreferences {
  if (typeof window === 'undefined') return defaultStreamPreferences;
  
  try {
    const saved = localStorage.getItem('streamPreferences');
    if (saved) {
      return { ...defaultStreamPreferences, ...JSON.parse(saved) };
    }
  } catch (error) {
    console.error('Failed to load stream preferences:', error);
  }
  
  return defaultStreamPreferences;
}

/**
 * Save user preferences to localStorage
 */
export function saveUserStreamPreferences(preferences: Partial<StreamPreferences>): void {
  if (typeof window === 'undefined') return;
  
  try {
    const current = getUserStreamPreferences();
    const updated = { ...current, ...preferences };
    localStorage.setItem('streamPreferences', JSON.stringify(updated));
  } catch (error) {
    console.error('Failed to save stream preferences:', error);
  }
}
