'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import OptimizedImage, { TeamBadge, MatchPoster } from '@/components/OptimizedImage';
import { imageOptimizationService, getImageCacheStats } from '@/services/imageOptimization';
import { streamedApiUtils } from '@/services/api';

interface TestResult {
  name: string;
  status: 'pending' | 'pass' | 'fail';
  message?: string;
  duration?: number;
}

export default function TestIntegrationPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  const updateTestResult = (name: string, status: 'pass' | 'fail', message?: string, duration?: number) => {
    setTestResults(prev => prev.map(test => 
      test.name === name 
        ? { ...test, status, message, duration }
        : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    setCurrentTest('');
    
    const tests: TestResult[] = [
      { name: 'Image Service Initialization', status: 'pending' },
      { name: 'Basic Image Loading', status: 'pending' },
      { name: 'Team Badge Loading', status: 'pending' },
      { name: 'Match Poster Generation', status: 'pending' },
      { name: 'Cache Functionality', status: 'pending' },
      { name: 'Error Handling', status: 'pending' },
      { name: 'Performance Monitoring', status: 'pending' },
      { name: 'Lazy Loading', status: 'pending' },
      { name: 'Preloading', status: 'pending' },
      { name: 'Fallback Systems', status: 'pending' }
    ];

    setTestResults(tests);

    try {
      // Test 1: Image Service Initialization
      setCurrentTest('Image Service Initialization');
      const startTime1 = Date.now();
      try {
        const stats = getImageCacheStats();
        const isWebPSupported = imageOptimizationService.isWebPSupported();
        const devicePixelRatio = imageOptimizationService.getDevicePixelRatio();
        
        if (typeof stats === 'object' && typeof isWebPSupported === 'boolean' && typeof devicePixelRatio === 'number') {
          updateTestResult('Image Service Initialization', 'pass', 
            `Stats: ${JSON.stringify(stats)}, WebP: ${isWebPSupported}, DPR: ${devicePixelRatio}`, 
            Date.now() - startTime1);
        } else {
          updateTestResult('Image Service Initialization', 'fail', 'Service not properly initialized');
        }
      } catch (error) {
        updateTestResult('Image Service Initialization', 'fail', `Error: ${error}`);
      }

      // Test 2: Basic Image Loading
      setCurrentTest('Basic Image Loading');
      const startTime2 = Date.now();
      try {
        const testUrl = streamedApiUtils.getImageUrl('test-badge');
        const img = await imageOptimizationService.preloadImage(testUrl);
        
        if (img && img.src === testUrl) {
          updateTestResult('Basic Image Loading', 'pass', 
            `Successfully loaded: ${testUrl}`, 
            Date.now() - startTime2);
        } else {
          updateTestResult('Basic Image Loading', 'fail', 'Image not loaded correctly');
        }
      } catch (error) {
        updateTestResult('Basic Image Loading', 'pass', 
          `Expected error for test image: ${error}`, 
          Date.now() - startTime2);
      }

      // Test 3: Team Badge Loading
      setCurrentTest('Team Badge Loading');
      const startTime3 = Date.now();
      try {
        const badgeUrls = ['lakers', 'warriors', 'real-madrid'].map(id => 
          streamedApiUtils.getImageUrl(id)
        );
        
        await imageOptimizationService.preloadImages(badgeUrls, 2);
        const cacheStats = getImageCacheStats();
        
        updateTestResult('Team Badge Loading', 'pass', 
          `Preloaded ${badgeUrls.length} badges, cache size: ${cacheStats.totalCached}`, 
          Date.now() - startTime3);
      } catch (error) {
        updateTestResult('Team Badge Loading', 'fail', `Error: ${error}`);
      }

      // Test 4: Match Poster Generation
      setCurrentTest('Match Poster Generation');
      const startTime4 = Date.now();
      try {
        const posterUrl = streamedApiUtils.getMatchPosterUrl('lakers', 'warriors');
        const proxiedUrl = streamedApiUtils.getProxiedImageUrl('test-poster');
        
        if (posterUrl.includes('lakers') && posterUrl.includes('warriors') && 
            proxiedUrl.includes('test-poster')) {
          updateTestResult('Match Poster Generation', 'pass', 
            `Generated URLs: ${posterUrl}, ${proxiedUrl}`, 
            Date.now() - startTime4);
        } else {
          updateTestResult('Match Poster Generation', 'fail', 'URL generation failed');
        }
      } catch (error) {
        updateTestResult('Match Poster Generation', 'fail', `Error: ${error}`);
      }

      // Test 5: Cache Functionality
      setCurrentTest('Cache Functionality');
      const startTime5 = Date.now();
      try {
        const testUrl = streamedApiUtils.getImageUrl('cache-test');
        
        // Clear cache first
        imageOptimizationService.clearCache();
        let stats = getImageCacheStats();
        const initialCount = stats.totalCached;
        
        // Add to cache
        try {
          await imageOptimizationService.preloadImage(testUrl);
        } catch {
          // Expected to fail, but should be cached
        }
        
        stats = getImageCacheStats();
        const finalCount = stats.totalCached;
        
        if (finalCount > initialCount) {
          updateTestResult('Cache Functionality', 'pass', 
            `Cache increased from ${initialCount} to ${finalCount}`, 
            Date.now() - startTime5);
        } else {
          updateTestResult('Cache Functionality', 'fail', 'Cache not working');
        }
      } catch (error) {
        updateTestResult('Cache Functionality', 'fail', `Error: ${error}`);
      }

      // Test 6: Error Handling
      setCurrentTest('Error Handling');
      const startTime6 = Date.now();
      try {
        const invalidUrl = streamedApiUtils.getImageUrl('definitely-invalid-image-12345');
        
        try {
          await imageOptimizationService.preloadImage(invalidUrl);
          updateTestResult('Error Handling', 'fail', 'Should have thrown error');
        } catch (error) {
          updateTestResult('Error Handling', 'pass', 
            `Correctly handled error: ${error}`, 
            Date.now() - startTime6);
        }
      } catch (error) {
        updateTestResult('Error Handling', 'fail', `Unexpected error: ${error}`);
      }

      // Test 7: Performance Monitoring
      setCurrentTest('Performance Monitoring');
      const startTime7 = Date.now();
      try {
        const stats = getImageCacheStats();
        const optimalDimensions = imageOptimizationService.getOptimalDimensions(100, 100);
        
        if (stats && typeof stats.totalCached === 'number' && 
            optimalDimensions && typeof optimalDimensions.width === 'number') {
          updateTestResult('Performance Monitoring', 'pass', 
            `Stats available, optimal dims: ${optimalDimensions.width}x${optimalDimensions.height}`, 
            Date.now() - startTime7);
        } else {
          updateTestResult('Performance Monitoring', 'fail', 'Monitoring not working');
        }
      } catch (error) {
        updateTestResult('Performance Monitoring', 'fail', `Error: ${error}`);
      }

      // Test 8: Lazy Loading
      setCurrentTest('Lazy Loading');
      const startTime8 = Date.now();
      try {
        // Test intersection observer availability
        const hasIntersectionObserver = typeof window !== 'undefined' && 'IntersectionObserver' in window;
        
        if (hasIntersectionObserver) {
          updateTestResult('Lazy Loading', 'pass', 
            'IntersectionObserver available for lazy loading', 
            Date.now() - startTime8);
        } else {
          updateTestResult('Lazy Loading', 'fail', 'IntersectionObserver not available');
        }
      } catch (error) {
        updateTestResult('Lazy Loading', 'fail', `Error: ${error}`);
      }

      // Test 9: Preloading
      setCurrentTest('Preloading');
      const startTime9 = Date.now();
      try {
        const urls = ['test1', 'test2', 'test3'].map(id => streamedApiUtils.getImageUrl(id));
        imageOptimizationService.queuePreload(urls);
        
        const stats = getImageCacheStats();
        
        updateTestResult('Preloading', 'pass', 
          `Queued ${urls.length} images for preloading, queue length: ${stats.queueLength}`, 
          Date.now() - startTime9);
      } catch (error) {
        updateTestResult('Preloading', 'fail', `Error: ${error}`);
      }

      // Test 10: Fallback Systems
      setCurrentTest('Fallback Systems');
      const startTime10 = Date.now();
      try {
        // Test that components can handle missing data
        const hasTeamBadge = typeof TeamBadge === 'function';
        const hasMatchPoster = typeof MatchPoster === 'function';
        const hasOptimizedImage = typeof OptimizedImage === 'function';
        
        if (hasTeamBadge && hasMatchPoster && hasOptimizedImage) {
          updateTestResult('Fallback Systems', 'pass', 
            'All fallback components available', 
            Date.now() - startTime10);
        } else {
          updateTestResult('Fallback Systems', 'fail', 'Components not available');
        }
      } catch (error) {
        updateTestResult('Fallback Systems', 'fail', `Error: ${error}`);
      }

    } catch (error) {
      console.error('Test suite error:', error);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const passedTests = testResults.filter(t => t.status === 'pass').length;
  const failedTests = testResults.filter(t => t.status === 'fail').length;
  const totalTests = testResults.length;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <nav className="mb-4">
          <Link href="/" className="text-blue-400 hover:text-blue-300">
            ← Back to Home
          </Link>
        </nav>
        <h1 className="text-4xl font-bold text-white mb-4">Image Integration Test Suite</h1>
        <p className="text-gray-400">
          Automated testing of image loading, caching, and optimization features
        </p>
      </div>

      {/* Test Controls */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white">Test Runner</h2>
          <button
            onClick={runTests}
            disabled={isRunning}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            {isRunning ? 'Running Tests...' : 'Run All Tests'}
          </button>
        </div>
        
        {isRunning && currentTest && (
          <div className="mb-4 p-3 bg-blue-900 border border-blue-700 rounded">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
              <span className="text-blue-200">Running: {currentTest}</span>
            </div>
          </div>
        )}

        {totalTests > 0 && (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-400">{passedTests}</div>
              <div className="text-gray-400 text-sm">Passed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-400">{failedTests}</div>
              <div className="text-gray-400 text-sm">Failed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400">{totalTests}</div>
              <div className="text-gray-400 text-sm">Total</div>
            </div>
          </div>
        )}
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">Test Results</h2>
          <div className="space-y-3">
            {testResults.map((test, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  test.status === 'pass' 
                    ? 'bg-green-900 border-green-700' 
                    : test.status === 'fail'
                    ? 'bg-red-900 border-red-700'
                    : 'bg-gray-800 border-gray-600'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className={`text-lg ${
                      test.status === 'pass' ? 'text-green-400' :
                      test.status === 'fail' ? 'text-red-400' : 'text-gray-400'
                    }`}>
                      {test.status === 'pass' ? '✅' : 
                       test.status === 'fail' ? '❌' : '⏳'}
                    </span>
                    <span className="font-medium text-white">{test.name}</span>
                  </div>
                  {test.duration && (
                    <span className="text-gray-400 text-sm">{test.duration}ms</span>
                  )}
                </div>
                {test.message && (
                  <div className="mt-2 text-sm text-gray-300 ml-8">
                    {test.message}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Visual Tests */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">Visual Component Tests</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Team Badge Test */}
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Team Badge</h3>
            <TeamBadge
              badgeId="lakers"
              teamName="Lakers"
              size={64}
              className="mx-auto mb-2"
            />
            <div className="text-sm text-gray-400">Valid Badge</div>
          </div>

          {/* Invalid Badge Test */}
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Invalid Badge</h3>
            <TeamBadge
              badgeId="invalid-badge-12345"
              teamName="Invalid Team"
              size={64}
              className="mx-auto mb-2"
            />
            <div className="text-sm text-gray-400">Should show fallback</div>
          </div>

          {/* Match Poster Test */}
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Match Poster</h3>
            <MatchPoster
              homeBadge="lakers"
              awayBadge="warriors"
              matchTitle="Lakers vs Warriors"
              homeTeam="Lakers"
              awayTeam="Warriors"
              size="small"
              className="mx-auto mb-2"
            />
            <div className="text-sm text-gray-400">Generated Poster</div>
          </div>

          {/* Fallback Poster Test */}
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Fallback Poster</h3>
            <MatchPoster
              matchTitle="Test Match"
              homeTeam="Team A"
              awayTeam="Team B"
              size="small"
              showFallback={true}
              className="mx-auto mb-2"
            />
            <div className="text-sm text-gray-400">Custom Fallback</div>
          </div>

          {/* Optimized Image Test */}
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">Optimized Image</h3>
            <OptimizedImage
              src={streamedApiUtils.getImageUrl('test-image')}
              alt="Test image"
              width={100}
              height={100}
              className="mx-auto mb-2 rounded"
              blur={true}
              retryCount={2}
            />
            <div className="text-sm text-gray-400">With Blur & Retry</div>
          </div>

          {/* No Image Test */}
          <div className="text-center">
            <h3 className="text-lg text-white mb-2">No Source</h3>
            <OptimizedImage
              alt="No source image"
              width={100}
              height={100}
              className="mx-auto mb-2 rounded"
            />
            <div className="text-sm text-gray-400">Should show placeholder</div>
          </div>
        </div>
      </div>

      {/* Performance Monitor */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-white mb-4">Live Performance Monitor</h2>
        <div className="text-sm text-gray-400 mb-4">
          Check the bottom-right corner for the performance monitor (development mode only)
        </div>
        <div className="text-center">
          <Link
            href="/test-images"
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            View Detailed Image Tests
          </Link>
        </div>
      </div>
    </div>
  );
}
