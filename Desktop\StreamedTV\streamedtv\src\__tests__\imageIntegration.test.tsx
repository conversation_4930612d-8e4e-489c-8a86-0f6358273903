/**
 * Image Integration Tests
 * End-to-end tests for the complete image system
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { act } from '@testing-library/react';

// Import components to test
import OptimizedImage, { TeamBadge, MatchPoster } from '@/components/OptimizedImage';
import ImagePerformanceMonitor from '@/components/ImagePerformanceMonitor';

// Mock API responses
const mockApiResponses = {
  badges: {
    'lakers': 'https://api.streamed.su/api/images/badge/lakers.webp',
    'warriors': 'https://api.streamed.su/api/images/badge/warriors.webp',
    'invalid': 'https://api.streamed.su/api/images/badge/invalid.webp'
  },
  posters: {
    'lakers-warriors': 'https://api.streamed.su/api/images/poster/lakers/warriors.webp',
    'custom-poster': 'https://api.streamed.su/api/images/proxy/custom-poster.webp'
  }
};

// Mock fetch for API calls
global.fetch = jest.fn((url: string) => {
  const urlStr = url.toString();
  
  if (urlStr.includes('invalid') || urlStr.includes('error')) {
    return Promise.resolve({
      ok: false,
      status: 404,
      blob: () => Promise.reject(new Error('Not found'))
    } as Response);
  }
  
  return Promise.resolve({
    ok: true,
    status: 200,
    blob: () => Promise.resolve(new Blob(['fake-image-data'], { type: 'image/webp' }))
  } as Response);
});

// Mock Image constructor for testing
class MockImage {
  src: string = '';
  onload: (() => void) | null = null;
  onerror: (() => void) | null = null;
  naturalWidth: number = 100;
  naturalHeight: number = 100;

  constructor() {
    setTimeout(() => {
      if (this.src.includes('invalid') || this.src.includes('error')) {
        this.onerror?.();
      } else {
        this.onload?.();
      }
    }, 10);
  }
}

(global as any).Image = MockImage;

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe('Image Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear any existing cache
    localStorage.clear();
  });

  describe('Complete Image Loading Flow', () => {
    it('should load team badges with proper fallbacks', async () => {
      render(
        <div>
          <TeamBadge badgeId="lakers" teamName="Lakers" size={32} />
          <TeamBadge badgeId="invalid" teamName="Invalid Team" size={32} />
          <TeamBadge teamName="No Badge Team" size={32} />
        </div>
      );

      // Valid badge should load
      const lakersImg = screen.getByAltText('Lakers badge');
      expect(lakersImg).toBeInTheDocument();

      // Invalid badge should show fallback
      const invalidImg = screen.getByAltText('Invalid Team badge');
      expect(invalidImg).toBeInTheDocument();

      // No badge should show initial
      const initial = screen.getByText('N');
      expect(initial).toBeInTheDocument();
    });

    it('should load match posters with proper priority', async () => {
      render(
        <div>
          <MatchPoster
            homeBadge="lakers"
            awayBadge="warriors"
            poster="custom-poster"
            matchTitle="Lakers vs Warriors"
            homeTeam="Lakers"
            awayTeam="Warriors"
            size="hero"
          />
          <MatchPoster
            homeBadge="lakers"
            awayBadge="warriors"
            matchTitle="Generated Poster"
            homeTeam="Lakers"
            awayTeam="Warriors"
            size="medium"
          />
          <MatchPoster
            matchTitle="Fallback Poster"
            homeTeam="Team A"
            awayTeam="Team B"
            size="small"
            showFallback={true}
          />
        </div>
      );

      // Hero poster should load with priority
      const heroImg = screen.getByAltText('Lakers vs Warriors poster');
      expect(heroImg).toHaveAttribute('loading', 'eager');

      // Medium poster should be lazy
      const mediumImg = screen.getByAltText('Generated Poster poster');
      expect(mediumImg).toHaveAttribute('loading', 'lazy');

      // Fallback should show custom design
      const fallbackTitle = screen.getByText('Fallback Poster');
      expect(fallbackTitle).toBeInTheDocument();
    });

    it('should handle mixed success and failure scenarios', async () => {
      render(
        <div>
          <OptimizedImage
            src="https://api.streamed.su/api/images/badge/lakers.webp"
            alt="Valid image"
            width={100}
            height={100}
          />
          <OptimizedImage
            src="https://api.streamed.su/api/images/badge/invalid.webp"
            alt="Invalid image"
            width={100}
            height={100}
            fallbackSrc="https://api.streamed.su/api/images/badge/fallback.webp"
          />
          <OptimizedImage
            src="https://api.streamed.su/api/images/badge/error.webp"
            alt="Error image"
            width={100}
            height={100}
          />
        </div>
      );

      // Valid image should load
      const validImg = screen.getByAltText('Valid image');
      expect(validImg).toBeInTheDocument();

      // Invalid image should try fallback
      const invalidImg = screen.getByAltText('Invalid image');
      expect(invalidImg).toBeInTheDocument();

      // Error image should show error state
      const errorImg = screen.getByAltText('Error image');
      expect(errorImg).toBeInTheDocument();
    });
  });

  describe('Performance Optimization Features', () => {
    it('should implement lazy loading correctly', async () => {
      const { container } = render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/lakers.webp"
          alt="Lazy image"
          width={100}
          height={100}
          loading="lazy"
        />
      );

      const img = screen.getByAltText('Lazy image');
      expect(img).toHaveAttribute('loading', 'lazy');

      // Should use intersection observer for lazy loading
      expect(mockIntersectionObserver).toHaveBeenCalled();
    });

    it('should implement priority loading for critical images', () => {
      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/lakers.webp"
          alt="Priority image"
          width={100}
          height={100}
          priority={true}
        />
      );

      const img = screen.getByAltText('Priority image');
      expect(img).toHaveAttribute('loading', 'eager');
      expect(img).toHaveAttribute('fetchpriority', 'high');
    });

    it('should handle retry logic for failed images', async () => {
      const onError = jest.fn();
      
      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/error.webp"
          alt="Retry image"
          width={100}
          height={100}
          retryCount={2}
          onError={onError}
        />
      );

      const img = screen.getByAltText('Retry image');
      
      // Simulate error
      fireEvent.error(img);
      
      // Should attempt retries
      await waitFor(() => {
        expect(onError).toHaveBeenCalled();
      }, { timeout: 3000 });
    });

    it('should show loading states and transitions', async () => {
      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/lakers.webp"
          alt="Loading image"
          width={100}
          height={100}
          blur={true}
        />
      );

      const img = screen.getByAltText('Loading image');
      
      // Should start with loading state
      expect(img).toHaveClass('opacity-0');
      
      // Simulate load
      fireEvent.load(img);
      
      // Should transition to loaded state
      await waitFor(() => {
        expect(img).toHaveClass('opacity-100');
      });
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should gracefully handle network errors', async () => {
      // Mock network error
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      render(
        <TeamBadge
          badgeId="network-error"
          teamName="Network Error Team"
          size={32}
        />
      );

      // Should show team initial as fallback
      await waitFor(() => {
        const initial = screen.getByText('N');
        expect(initial).toBeInTheDocument();
      });
    });

    it('should handle malformed image data', async () => {
      // Mock malformed response
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
        blob: () => Promise.resolve(new Blob(['invalid-data'], { type: 'text/plain' }))
      } as Response);

      const onError = jest.fn();

      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/malformed.webp"
          alt="Malformed image"
          width={100}
          height={100}
          onError={onError}
        />
      );

      // Should handle error gracefully
      await waitFor(() => {
        expect(onError).toHaveBeenCalled();
      });
    });

    it('should provide meaningful error information', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/debug-error.webp"
          alt="Debug image"
          width={100}
          height={100}
        />
      );

      const img = screen.getByAltText('Debug image');
      fireEvent.error(img);

      // Should log error in development
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Failed to load image'),
          expect.any(Object)
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Performance Monitoring', () => {
    it('should track image loading performance', async () => {
      render(
        <div>
          <ImagePerformanceMonitor show={true} />
          <TeamBadge badgeId="lakers" teamName="Lakers" size={32} />
          <TeamBadge badgeId="warriors" teamName="Warriors" size={32} />
        </div>
      );

      // Performance monitor should be visible
      const monitor = screen.getByText('Image Performance');
      expect(monitor).toBeInTheDocument();

      // Should show cache statistics
      const cachedImages = screen.getByText(/Cached Images:/);
      expect(cachedImages).toBeInTheDocument();
    });

    it('should provide cache management controls', async () => {
      render(<ImagePerformanceMonitor show={true} />);

      // Should have clear cache button
      const clearButton = screen.getByText('Clear Cache');
      expect(clearButton).toBeInTheDocument();

      // Should be clickable
      fireEvent.click(clearButton);
      
      // Cache should be cleared (tested via stats update)
      await waitFor(() => {
        expect(clearButton).toBeInTheDocument();
      });
    });
  });

  describe('Responsive and Progressive Loading', () => {
    it('should adapt to different screen sizes', () => {
      // Mock different viewport sizes
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      });

      render(
        <MatchPoster
          homeBadge="lakers"
          awayBadge="warriors"
          matchTitle="Responsive Test"
          size="large"
        />
      );

      const img = screen.getByAltText('Responsive Test poster');
      expect(img).toHaveAttribute('width', '400');
      expect(img).toHaveAttribute('height', '240');
    });

    it('should handle high-DPI displays', () => {
      // Mock high-DPI display
      Object.defineProperty(window, 'devicePixelRatio', {
        writable: true,
        configurable: true,
        value: 2,
      });

      render(
        <TeamBadge
          badgeId="lakers"
          teamName="Lakers"
          size={32}
        />
      );

      // Should render at appropriate size for high-DPI
      const img = screen.getByAltText('Lakers badge');
      expect(img).toBeInTheDocument();
    });
  });

  describe('Accessibility and User Experience', () => {
    it('should provide proper alt text for all images', () => {
      render(
        <div>
          <TeamBadge badgeId="lakers" teamName="Lakers" size={32} />
          <MatchPoster
            homeBadge="lakers"
            awayBadge="warriors"
            matchTitle="Lakers vs Warriors"
            homeTeam="Lakers"
            awayTeam="Warriors"
            size="medium"
          />
        </div>
      );

      const badgeImg = screen.getByAltText('Lakers badge');
      const posterImg = screen.getByAltText('Lakers vs Warriors poster');
      
      expect(badgeImg).toBeInTheDocument();
      expect(posterImg).toBeInTheDocument();
    });

    it('should maintain layout stability during loading', () => {
      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/lakers.webp"
          alt="Stable layout"
          width={100}
          height={100}
        />
      );

      const container = screen.getByAltText('Stable layout').parentElement;
      expect(container).toHaveStyle({ width: '100px', height: '100px' });
    });

    it('should provide smooth loading transitions', async () => {
      render(
        <OptimizedImage
          src="https://api.streamed.su/api/images/badge/lakers.webp"
          alt="Smooth transition"
          width={100}
          height={100}
          blur={true}
        />
      );

      const img = screen.getByAltText('Smooth transition');
      
      // Should have transition classes
      expect(img).toHaveClass('transition-opacity');
      
      // Should start transparent
      expect(img).toHaveClass('opacity-0');
      
      // Simulate load
      fireEvent.load(img);
      
      // Should become visible
      await waitFor(() => {
        expect(img).toHaveClass('opacity-100');
      });
    });
  });
});
