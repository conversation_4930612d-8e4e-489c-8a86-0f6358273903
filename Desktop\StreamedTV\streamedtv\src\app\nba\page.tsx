'use client';

import React from 'react';
import Link from 'next/link';
import { useMatches, usePopularMatches } from '@/hooks/useApi';
import { APIMatch } from '@/types/api';
import { streamedApiUtils } from '@/services/api';
import { TeamBadge, MatchPoster } from '@/components/OptimizedImage';
import { useMatchImagePreloader, useIntelligentPreloader } from '@/hooks/useOptimizedImages';

// Helper function to check if match is live
const isMatchLive = (match: APIMatch) => {
  return streamedApiUtils.isMatchLive(match.date);
};

// Helper function to get team names
const getTeamNames = (match: APIMatch) => {
  const homeTeam = match.teams?.home?.name || 'Home Team';
  const awayTeam = match.teams?.away?.name || 'Away Team';
  return { homeTeam, awayTeam };
};

export default function NBAPage() {
  const { data: nbaMatches, loading, error } = useMatches('basketball');
  const { data: popularMatches, loading: popularLoading } = usePopularMatches('basketball');

  // Optimized image loading
  const { preloadImagesForPage } = useIntelligentPreloader();
  const matchImagePreloader = useMatchImagePreloader(data || []);
  const popularImagePreloader = useMatchImagePreloader(popularMatches || []);

  // Preload images when data is available
  React.useEffect(() => {
    if (data && data.length > 0) {
      preloadImagesForPage('sport', { matches: data });
    }
  }, [data, preloadImagesForPage]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <ol className="flex items-center space-x-2 text-sm text-gray-400">
          <li>
            <Link href="/" className="hover:text-white transition-colors">
              Home
            </Link>
          </li>
          <li>/</li>
          <li className="text-white">NBA</li>
        </ol>
      </nav>

      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
          NBA Live Streams
        </h1>
        <p className="text-gray-400 text-lg">
          Watch live NBA games in HD quality. All games available for free streaming.
        </p>
        {loading && (
          <div className="mt-4 text-blue-400">Loading NBA matches...</div>
        )}
        {error && (
          <div className="mt-4 text-red-400">Error loading matches: {error}</div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading NBA matches...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-900 border border-red-700 rounded-lg p-6 mb-8">
          <h3 className="text-red-400 font-semibold mb-2">Error Loading Matches</h3>
          <p className="text-red-300">{error}</p>
        </div>
      )}

      {/* Popular Matches Section */}
      {!popularLoading && popularMatches && popularMatches.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">⭐ Popular Basketball Matches</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {popularMatches.slice(0, 3).map((match) => {
              const { homeTeam, awayTeam } = getTeamNames(match);
              const isLive = isMatchLive(match);

              return (
                <Link
                  key={match.id}
                  href={`/match/${match.id}`}
                  className={`bg-gray-900 border rounded-lg overflow-hidden hover:bg-gray-800 transition-colors group ${
                    isLive ? 'border-red-500' : 'border-yellow-500'
                  }`}
                >
                  {/* Match Poster */}
                  <div className="relative">
                    <MatchPoster
                      homeBadge={match.teams?.home?.badge}
                      awayBadge={match.teams?.away?.badge}
                      poster={match.poster}
                      matchTitle={match.title}
                      homeTeam={homeTeam}
                      awayTeam={awayTeam}
                      size="small"
                      className="w-full"
                    />
                    {/* Status overlay */}
                    <div className="absolute top-2 left-2">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                        isLive ? 'bg-red-600 text-white' : 'bg-yellow-600 text-white'
                      }`}>
                        {isLive ? '🔴 LIVE' : '⭐ POPULAR'}
                      </span>
                    </div>
                  </div>

                  <div className="p-4">
                    {/* Team badges */}
                    <div className="flex items-center justify-between mb-3">
                      <TeamBadge
                        badgeId={match.teams?.home?.badge}
                        teamName={homeTeam}
                        size={24}
                      />
                      <span className="text-gray-400 text-sm font-medium">VS</span>
                      <TeamBadge
                        badgeId={match.teams?.away?.badge}
                        teamName={awayTeam}
                        size={24}
                      />
                    </div>

                    <h3 className="text-white font-semibold mb-2 group-hover:text-blue-400 transition-colors line-clamp-2">
                      {match.title}
                    </h3>
                    <div className="text-gray-400 text-sm">
                      <div>{streamedApiUtils.formatMatchDate(match.date)} • {streamedApiUtils.formatMatchTime(match.date)}</div>
                      <div>{match.sources.length} stream{match.sources.length !== 1 ? 's' : ''} available</div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      )}

      {/* All Matches Grid */}
      {!loading && !error && (
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">All Basketball Matches</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {nbaMatches && nbaMatches.length > 0 ? (
              nbaMatches.map((match) => {
                const { homeTeam, awayTeam } = getTeamNames(match);
                const isLive = isMatchLive(match);

                return (
                  <div
                    key={match.id}
                    className={`bg-gray-900 border rounded-lg p-6 hover:bg-gray-800 transition-colors ${
                      isLive ? 'border-red-500' : 'border-gray-800'
                    }`}
                  >
                    {/* Match Header */}
                    <div className="flex justify-between items-start mb-4">
                      <span className={`text-xs px-2 py-1 rounded-full text-white ${
                        isLive ? 'bg-red-600' : 'bg-blue-600'
                      }`}>
                        {isLive ? 'LIVE' : 'SCHEDULED'}
                      </span>
                      <div className="flex items-center space-x-2">
                        {match.popular && <span className="text-yellow-400 text-sm">⭐</span>}
                        <span className="text-gray-400 text-sm">{match.category}</span>
                      </div>
                    </div>

                    {/* Team badges */}
                    <div className="flex items-center justify-between mb-3">
                      <TeamBadge
                        badgeId={match.teams?.home?.badge}
                        teamName={homeTeam}
                        size={32}
                      />
                      <span className="text-gray-400 text-sm">VS</span>
                      <TeamBadge
                        badgeId={match.teams?.away?.badge}
                        teamName={awayTeam}
                        size={32}
                      />
                    </div>

                    {/* Match Title */}
                    <h3 className="text-white font-semibold text-lg mb-2">
                      {match.title}
                    </h3>

                    {/* Match Details */}
                    <div className="text-gray-400 text-sm mb-4">
                      <div className="flex justify-between">
                        <span>{streamedApiUtils.formatMatchDate(match.date)}</span>
                        <span>{streamedApiUtils.formatMatchTime(match.date)}</span>
                      </div>
                    </div>

                    {/* Stream Info */}
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500 text-sm">
                        {match.sources.length} stream{match.sources.length !== 1 ? 's' : ''} available
                      </span>

                      {match.sources.length > 0 && (
                        <Link
                          href={`/match/${match.id}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        >
                          Watch Now
                        </Link>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="col-span-full text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="h-16 w-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>
                </div>
                <h3 className="text-xl text-white mb-2">No NBA matches available</h3>
                <p className="text-gray-400">Check back later for live games and streams.</p>
                <Link
                  href="/"
                  className="inline-block mt-4 text-blue-400 hover:text-blue-300 transition-colors"
                >
                  Browse other sports →
                </Link>
              </div>
            )}
          </div>
        </div>
      )}

    </div>
  );
}
