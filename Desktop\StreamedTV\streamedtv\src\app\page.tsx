'use client';

import React from 'react';
import Link from 'next/link';
import { useSports, useAllMatches, useLiveMatches, useTodayMatches, usePopularLiveMatches } from '@/hooks/useApi';
import { APIMatch } from '@/types/api';
import { streamedApiUtils } from '@/services/api';
import { TeamBadge, MatchPoster } from '@/components/OptimizedImage';
import { useMatchImagePreloader, useIntelligentPreloader } from '@/hooks/useOptimizedImages';

// Loading component
const LoadingCard = () => (
  <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 animate-pulse">
    <div className="h-6 bg-gray-700 rounded mb-4"></div>
    <div className="space-y-3">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex justify-between items-center">
          <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-700 rounded w-16"></div>
        </div>
      ))}
    </div>
  </div>
);

// Helper function to group matches by sport (updated for APIMatch)
const groupMatchesBySport = (matches: APIMatch[]) => {
  const grouped: { [key: string]: APIMatch[] } = {};

  matches.forEach(match => {
    const sport = match.category || 'Other';
    if (!grouped[sport]) {
      grouped[sport] = [];
    }
    grouped[sport].push(match);
  });

  return grouped;
};

// Helper function to check if match is live (based on timestamp and current time)
const isMatchLive = (match: APIMatch) => {
  return streamedApiUtils.isMatchLive(match.date);
};

// Helper function to get team names from APIMatch
const getTeamNames = (match: APIMatch) => {
  const homeTeam = match.teams?.home?.name || 'Home Team';
  const awayTeam = match.teams?.away?.name || 'Away Team';
  return { homeTeam, awayTeam };
};

export default function Home() {
  const { data: sports, loading: sportsLoading } = useSports();
  const { data: allMatches, loading: matchesLoading } = useAllMatches();
  const { data: liveMatches, loading: liveLoading } = useLiveMatches();
  const { data: todayMatches, loading: todayLoading } = useTodayMatches();

  // Optimized image loading
  const { preloadImagesForPage } = useIntelligentPreloader();
  const liveImagePreloader = useMatchImagePreloader(liveMatches || []);

  // Group matches by sport for display
  const matchesBySport = allMatches ? groupMatchesBySport(allMatches) : {};

  // Preload images when data is available
  React.useEffect(() => {
    if (liveMatches && liveMatches.length > 0) {
      preloadImagesForPage('home', { matches: liveMatches });
    }
  }, [liveMatches, preloadImagesForPage]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
          Watch Any Live Sport Online
        </h1>
        <p className="text-xl text-gray-400 mb-8">
          Best source to watch NBA, NHL, MLB, UFC for free!
        </p>
        <div className="bg-blue-600 text-white px-6 py-3 rounded-lg inline-block mb-8">
          <p className="font-medium">
            Important: Please use the strmd.link web address instead of Google to access the original Streamed URLs.{' '}
            <Link href="https://strmd.link" className="underline hover:no-underline" target="_blank" rel="noopener noreferrer">
              Click here to explore our mirror websites.
            </Link>
          </p>
        </div>
      </div>

      {/* Live Matches Section */}
      {!liveLoading && liveMatches && liveMatches.length > 0 && (
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-white mb-6">🔴 Live Now</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {liveMatches.slice(0, 6).map((match) => {
              const { homeTeam, awayTeam } = getTeamNames(match);
              return (
                <Link
                  key={match.id}
                  href={`/match/${match.id}`}
                  className="bg-gray-900 border border-red-500 rounded-lg overflow-hidden hover:bg-gray-800 transition-colors group"
                >
                  {/* Match Poster */}
                  <div className="relative">
                    <MatchPoster
                      homeBadge={match.teams?.home?.badge}
                      awayBadge={match.teams?.away?.badge}
                      poster={match.poster}
                      matchTitle={match.title}
                      homeTeam={homeTeam}
                      awayTeam={awayTeam}
                      size="small"
                      className="w-full"
                    />
                    {/* Live overlay */}
                    <div className="absolute top-2 left-2">
                      <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                        🔴 LIVE
                      </span>
                    </div>
                    <div className="absolute top-2 right-2">
                      <span className="bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                        {match.category}
                      </span>
                    </div>
                  </div>

                  <div className="p-4">
                    {/* Team badges */}
                    <div className="flex items-center justify-between mb-3">
                      <TeamBadge
                        badgeId={match.teams?.home?.badge}
                        teamName={homeTeam}
                        size={24}
                      />
                      <span className="text-gray-400 text-sm font-medium">VS</span>
                      <TeamBadge
                        badgeId={match.teams?.away?.badge}
                        teamName={awayTeam}
                        size={24}
                      />
                    </div>

                    <h3 className="text-white font-semibold mb-2 group-hover:text-blue-400 transition-colors line-clamp-2">
                      {match.title}
                    </h3>
                    <div className="text-gray-400 text-sm">
                      <div>{streamedApiUtils.formatMatchTime(match.date)}</div>
                      <div>{match.sources.length} stream{match.sources.length !== 1 ? 's' : ''} available</div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      )}

      {/* Today's Matches Section */}
      {!todayLoading && todayMatches && todayMatches.length > 0 && (
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-white mb-6">📅 Today's Matches</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {todayMatches.slice(0, 8).map((match) => {
              const { homeTeam, awayTeam } = getTeamNames(match);
              const isLive = isMatchLive(match);

              return (
                <Link
                  key={match.id}
                  href={`/match/${match.id}`}
                  className={`bg-gray-900 border rounded-lg p-4 hover:bg-gray-800 transition-colors group ${
                    isLive ? 'border-red-500' : 'border-gray-800'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      isLive ? 'bg-red-600 text-white' : 'bg-blue-600 text-white'
                    }`}>
                      {isLive ? 'LIVE' : 'TODAY'}
                    </span>
                    <span className="text-gray-400 text-sm">{match.category}</span>
                  </div>

                  <h4 className="text-white font-medium mb-1 group-hover:text-blue-400 transition-colors text-sm">
                    {match.title}
                  </h4>
                  <div className="text-gray-400 text-xs">
                    <div>{streamedApiUtils.formatMatchTime(match.date)}</div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      )}

      {/* Sports Categories Grid */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-white mb-6">Sports Categories</h2>
        {sportsLoading || matchesLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <LoadingCard key={i} />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sports && sports.map((sport) => {
              const sportMatches = matchesBySport[sport.id] || [];
              const liveCount = sportMatches.filter(match => isMatchLive(match)).length;
              const todayCount = todayMatches?.filter(match => match.category === sport.id).length || 0;

              return (
                <div key={sport.id} className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-white mb-4 border-b border-gray-700 pb-2">
                    {sport.name}
                  </h3>
                  <div className="space-y-3">
                    <Link
                      href={`/sport/${sport.id}`}
                      className="flex justify-between items-center text-gray-300 hover:text-white transition-colors duration-200 group"
                    >
                      <span className="group-hover:underline">All Matches</span>
                      <span className="text-sm text-gray-500 bg-gray-800 px-2 py-1 rounded">
                        {sportMatches.length} total
                      </span>
                    </Link>
                    {liveCount > 0 && (
                      <Link
                        href={`/sport/${sport.id}?filter=live`}
                        className="flex justify-between items-center text-gray-300 hover:text-white transition-colors duration-200 group"
                      >
                        <span className="group-hover:underline">🔴 Live</span>
                        <span className="text-sm text-red-500 bg-gray-800 px-2 py-1 rounded">
                          {liveCount} live
                        </span>
                      </Link>
                    )}
                    {todayCount > 0 && (
                      <Link
                        href={`/sport/${sport.id}?filter=today`}
                        className="flex justify-between items-center text-gray-300 hover:text-white transition-colors duration-200 group"
                      >
                        <span className="group-hover:underline">📅 Today</span>
                        <span className="text-sm text-blue-500 bg-gray-800 px-2 py-1 rounded">
                          {todayCount} today
                        </span>
                      </Link>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Additional Info Section */}
      <div className="mt-16 text-center">
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-white mb-4">
            Stream Live Sports for Free
          </h3>
          <p className="text-gray-400 mb-6 max-w-3xl mx-auto">
            Access live streams of your favorite sports including NBA, NFL, NHL, MLB, UFC, and more.
            Our platform provides high-quality streams with minimal buffering for the best viewing experience.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <div className="bg-gray-800 px-4 py-2 rounded-lg">
              <span className="text-green-400 font-semibold">✓</span>
              <span className="text-white ml-2">HD Quality</span>
            </div>
            <div className="bg-gray-800 px-4 py-2 rounded-lg">
              <span className="text-green-400 font-semibold">✓</span>
              <span className="text-white ml-2">No Registration</span>
            </div>
            <div className="bg-gray-800 px-4 py-2 rounded-lg">
              <span className="text-green-400 font-semibold">✓</span>
              <span className="text-white ml-2">Multiple Sports</span>
            </div>
            <div className="bg-gray-800 px-4 py-2 rounded-lg">
              <span className="text-green-400 font-semibold">✓</span>
              <span className="text-white ml-2">Mobile Friendly</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
